{"name": "@fireproof/core-test", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "main": "./index.js", "scripts": {"pack": "core-cli build --doPack", "build": "core-cli tsc", "test": "vitest --run", "publish": "core-cli build"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "peerDependencies": {"react": ">=18.0.0"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/core": "workspace:0.0.0", "@fireproof/core-base": "workspace:0.0.0", "@fireproof/core-blockstore": "workspace:0.0.0", "@fireproof/core-gateways-base": "workspace:0.0.0", "@fireproof/core-gateways-file": "workspace:0.0.0", "@fireproof/core-gateways-indexeddb": "workspace:0.0.0", "@fireproof/core-gateways-memory": "workspace:0.0.0", "@fireproof/core-keybag": "workspace:0.0.0", "@fireproof/core-protocols-cloud": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/core-types-blockstore": "workspace:0.0.0", "@fireproof/core-types-protocols-cloud": "workspace:0.0.0", "@fireproof/core-types-runtime": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "@ipld/car": "^5.4.2", "@ipld/dag-cbor": "^9.2.4", "@ipld/dag-json": "^10.2.5", "@types/node": "^24.1.0", "cborg": "^4.2.12", "charwise": "^3.0.1", "use-fireproof": "workspace:0.0.0", "uuidv7": "^1.0.2"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@vitest/browser": "^3.2.4", "playwright": "^1.54.1", "playwright-chromium": "^1.54.1", "vitest": "^3.2.4", "zx": "^8.7.1"}}