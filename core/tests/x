$ [92mcd[39m /Users/<USER>/Software/fproof/fireproof/core/tests
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zcXfnfUFH",
  "url": "indexeddb://fp?name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zcXfnfUFH",
    "url": "indexeddb://fp?name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zcXfnfUFH",
  "url": "indexeddb://fp?name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2VsYTzKcs",
  "url": "indexeddb://fp?key=main&name=indexdb-z2XerEPmkQ&runtime=browser&store=meta&storekey=%40indexdb-z2XerEPmkQ-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2VsYTzKcs",
    "url": "indexeddb://fp?key=main&name=indexdb-z2XerEPmkQ&runtime=browser&store=meta&storekey=%40indexdb-z2XerEPmkQ-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zj5ccDHxG",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zj5ccDHxG",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zj5ccDHxG",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2XerEPmkQ&runtime=browser&store=wal&storekey=%40indexdb-z2XerEPmkQ-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zBsqMAUSQ",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2XerEPmkQ&runtime=browser&store=meta&storekey=%40indexdb-z2XerEPmkQ-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'onClosed'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zBsqMAUSQ",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2XerEPmkQ&runtime=browser&store=meta&storekey=%40indexdb-z2XerEPmkQ-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zAuHAogZ1",
  "url": "indexeddb://fp?name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zAuHAogZ1",
    "url": "indexeddb://fp?name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zAuHAogZ1",
  "url": "indexeddb://fp?name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2uMuDzaVz",
  "url": "indexeddb://fp?key=main&name=indexdb-z2Vkyqtwkd&runtime=browser&store=meta&storekey=%40indexdb-z2Vkyqtwkd-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2uMuDzaVz",
    "url": "indexeddb://fp?key=main&name=indexdb-z2Vkyqtwkd&runtime=browser&store=meta&storekey=%40indexdb-z2Vkyqtwkd-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zE7UNRhaV",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zE7UNRhaV",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zE7UNRhaV",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2Vkyqtwkd&runtime=browser&store=wal&storekey=%40indexdb-z2Vkyqtwkd-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2neWjoNk9",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2Vkyqtwkd&runtime=browser&store=meta&storekey=%40indexdb-z2Vkyqtwkd-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2neWjoNk9",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2Vkyqtwkd&runtime=browser&store=meta&storekey=%40indexdb-z2Vkyqtwkd-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zqptzGo4r",
  "url": "indexeddb://fp?name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zqptzGo4r",
    "url": "indexeddb://fp?name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zqptzGo4r",
  "url": "indexeddb://fp?name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zmmNmAfC4",
  "url": "indexeddb://fp?key=main&name=indexdb-z3pdkgpGK&runtime=browser&store=meta&storekey=%40indexdb-z3pdkgpGK-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zmmNmAfC4",
    "url": "indexeddb://fp?key=main&name=indexdb-z3pdkgpGK&runtime=browser&store=meta&storekey=%40indexdb-z3pdkgpGK-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2PDmWK5ub",
  "url": "indexeddb://fp?index=idx&name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2PDmWK5ub",
    "url": "indexeddb://fp?index=idx&name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2PDmWK5ub",
  "url": "indexeddb://fp?index=idx&name=indexdb-z3pdkgpGK&runtime=browser&store=wal&storekey=%40indexdb-z3pdkgpGK-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2HMexbpBJ",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z3pdkgpGK&runtime=browser&store=meta&storekey=%40indexdb-z3pdkgpGK-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'get'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2HMexbpBJ",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z3pdkgpGK&runtime=browser&store=meta&storekey=%40indexdb-z3pdkgpGK-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z3BX82m68F",
  "url": "indexeddb://fp?name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z3BX82m68F",
    "url": "indexeddb://fp?name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z3BX82m68F",
  "url": "indexeddb://fp?name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z7vxEkAya",
  "url": "indexeddb://fp?key=main&name=indexdb-zQ1EWML8X&runtime=browser&store=meta&storekey=%40indexdb-zQ1EWML8X-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z7vxEkAya",
    "url": "indexeddb://fp?key=main&name=indexdb-zQ1EWML8X&runtime=browser&store=meta&storekey=%40indexdb-zQ1EWML8X-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z39rEyt4C7",
  "url": "indexeddb://fp?index=idx&name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z39rEyt4C7",
    "url": "indexeddb://fp?index=idx&name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z39rEyt4C7",
  "url": "indexeddb://fp?index=idx&name=indexdb-zQ1EWML8X&runtime=browser&store=wal&storekey=%40indexdb-zQ1EWML8X-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z383VEb5My",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-zQ1EWML8X&runtime=browser&store=meta&storekey=%40indexdb-zQ1EWML8X-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'query'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z383VEb5My",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-zQ1EWML8X&runtime=browser&store=meta&storekey=%40indexdb-zQ1EWML8X-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2xLzx7a6N",
  "url": "indexeddb://fp?name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2xLzx7a6N",
    "url": "indexeddb://fp?name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2xLzx7a6N",
  "url": "indexeddb://fp?name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2psEBJ8YV",
  "url": "indexeddb://fp?key=main&name=indexdb-z2CcupTLMf&runtime=browser&store=meta&storekey=%40indexdb-z2CcupTLMf-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2psEBJ8YV",
    "url": "indexeddb://fp?key=main&name=indexdb-z2CcupTLMf&runtime=browser&store=meta&storekey=%40indexdb-z2CcupTLMf-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z25Dpr4XQ6",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z25Dpr4XQ6",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z25Dpr4XQ6",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2CcupTLMf&runtime=browser&store=wal&storekey=%40indexdb-z2CcupTLMf-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zRxDGq9Fx",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2CcupTLMf&runtime=browser&store=meta&storekey=%40indexdb-z2CcupTLMf-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'changes'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zRxDGq9Fx",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2CcupTLMf&runtime=browser&store=meta&storekey=%40indexdb-z2CcupTLMf-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2DKh93f6d",
  "url": "indexeddb://fp?name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2DKh93f6d",
    "url": "indexeddb://fp?name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2DKh93f6d",
  "url": "indexeddb://fp?name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2KNwx4T5y",
  "url": "indexeddb://fp?key=main&name=indexdb-z79MNCqrP&runtime=browser&store=meta&storekey=%40indexdb-z79MNCqrP-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2KNwx4T5y",
    "url": "indexeddb://fp?key=main&name=indexdb-z79MNCqrP&runtime=browser&store=meta&storekey=%40indexdb-z79MNCqrP-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zNqSf5wLE",
  "url": "indexeddb://fp?index=idx&name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zNqSf5wLE",
    "url": "indexeddb://fp?index=idx&name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zNqSf5wLE",
  "url": "indexeddb://fp?index=idx&name=indexdb-z79MNCqrP&runtime=browser&store=wal&storekey=%40indexdb-z79MNCqrP-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zT2jGcoD3",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z79MNCqrP&runtime=browser&store=meta&storekey=%40indexdb-z79MNCqrP-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'compact'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zT2jGcoD3",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z79MNCqrP&runtime=browser&store=meta&storekey=%40indexdb-z79MNCqrP-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zBv7WcyE7",
  "url": "indexeddb://fp?name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zBv7WcyE7",
    "url": "indexeddb://fp?name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zBv7WcyE7",
  "url": "indexeddb://fp?name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zAQwczWT1",
  "url": "indexeddb://fp?key=main&name=indexdb-z2ZLyg4LPg&runtime=browser&store=meta&storekey=%40indexdb-z2ZLyg4LPg-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zAQwczWT1",
    "url": "indexeddb://fp?key=main&name=indexdb-z2ZLyg4LPg&runtime=browser&store=meta&storekey=%40indexdb-z2ZLyg4LPg-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zZLgoqGz5",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zZLgoqGz5",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zZLgoqGz5",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2ZLyg4LPg&runtime=browser&store=wal&storekey=%40indexdb-z2ZLyg4LPg-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "zxXtyV2EA",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2ZLyg4LPg&runtime=browser&store=meta&storekey=%40indexdb-z2ZLyg4LPg-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is not creating a db[2m > [22m[2mtest 'del'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "zxXtyV2EA",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2ZLyg4LPg&runtime=browser&store=meta&storekey=%40indexdb-z2ZLyg4LPg-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zaytDQoRw",
  "url": "indexeddb://fp?name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zaytDQoRw",
    "url": "indexeddb://fp?name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zaytDQoRw",
  "url": "indexeddb://fp?name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2vcauMJD5",
  "url": "indexeddb://fp?key=main&name=indexdb-zdWdkNwXi&runtime=browser&store=meta&storekey=%40indexdb-zdWdkNwXi-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2vcauMJD5",
    "url": "indexeddb://fp?key=main&name=indexdb-zdWdkNwXi&runtime=browser&store=meta&storekey=%40indexdb-zdWdkNwXi-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zJwCyXfhX",
  "url": "indexeddb://fp?index=idx&name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zJwCyXfhX",
    "url": "indexeddb://fp?index=idx&name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zJwCyXfhX",
  "url": "indexeddb://fp?index=idx&name=indexdb-zdWdkNwXi&runtime=browser&store=wal&storekey=%40indexdb-zdWdkNwXi-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2yDYbyGA1",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-zdWdkNwXi&runtime=browser&store=meta&storekey=%40indexdb-zdWdkNwXi-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'put'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2yDYbyGA1",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-zdWdkNwXi&runtime=browser&store=meta&storekey=%40indexdb-zdWdkNwXi-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2VXKUeWmK",
  "url": "indexeddb://fp?name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2VXKUeWmK",
    "url": "indexeddb://fp?name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2VXKUeWmK",
  "url": "indexeddb://fp?name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2AWKHS2Gh",
  "url": "indexeddb://fp?key=main&name=indexdb-z2eHQGwVpe&runtime=browser&store=meta&storekey=%40indexdb-z2eHQGwVpe-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2AWKHS2Gh",
    "url": "indexeddb://fp?key=main&name=indexdb-z2eHQGwVpe&runtime=browser&store=meta&storekey=%40indexdb-z2eHQGwVpe-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2Fe7GqFLA",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2Fe7GqFLA",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2Fe7GqFLA",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2eHQGwVpe&runtime=browser&store=wal&storekey=%40indexdb-z2eHQGwVpe-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2v8tJ4uAV",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2eHQGwVpe&runtime=browser&store=meta&storekey=%40indexdb-z2eHQGwVpe-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2maction is creating a db[2m > [22m[2mtest 'bulk'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2v8tJ4uAV",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2eHQGwVpe&runtime=browser&store=meta&storekey=%40indexdb-z2eHQGwVpe-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "z2nE9TwLvm",
  "url": "indexeddb://fp?name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "z2nE9TwLvm",
    "url": "indexeddb://fp?name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "z2nE9TwLvm",
  "url": "indexeddb://fp?name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2BKuLaDHv",
  "url": "indexeddb://fp?key=main&name=indexdb-z2SKx9CJsc&runtime=browser&store=meta&storekey=%40indexdb-z2SKx9CJsc-meta%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2BKuLaDHv",
    "url": "indexeddb://fp?key=main&name=indexdb-z2SKx9CJsc&runtime=browser&store=meta&storekey=%40indexdb-z2SKx9CJsc-meta%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error get",
  "this": "zGGP3uzZF",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "WALStoreImpl",
    "msg": "error get",
    "this": "zGGP3uzZF",
    "url": "indexeddb://fp?index=idx&name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "WALStoreImpl",
  "msg": "error loading wal",
  "this": "zGGP3uzZF",
  "url": "indexeddb://fp?index=idx&name=indexdb-z2SKx9CJsc&runtime=browser&store=wal&storekey=%40indexdb-z2SKx9CJsc-wal-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": "Method not implemented.",
  "level": "error",
  "module": "MetaStoreImpl",
  "msg": "gateway get",
  "this": "z2o6MGuqjF",
  "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2SKx9CJsc&runtime=browser&store=meta&storekey=%40indexdb-z2SKx9CJsc-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
}
[90mstderr[2m | gateway/indexeddb/create-db-on-write.test.ts[2m > [22m[2mIndexedDB create on write[2m > [22m[2mcombine not and creating[2m > [22m[2mnot creating 'allDocs'
[22m[39m{
  "error": {
    "error": "Method not implemented.",
    "level": "error",
    "module": "MetaStoreImpl",
    "msg": "gateway get",
    "this": "z2o6MGuqjF",
    "url": "indexeddb://fp?index=idx&key=main&name=indexdb-z2SKx9CJsc&runtime=browser&store=meta&storekey=%40indexdb-z2SKx9CJsc-meta-idx%40&urlGen=default&version=v0.19-indexeddb",
  },
  "level": "error",
  "module": "Loader",
  "msg": "error waiting for first meta",
}

[31m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[39m[1m[41m Failed Tests 2 [49m[22m[31m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[39m

[41m[1m FAIL [22m[49m [30m[42m indexeddb (chromium) [49m[39m gateway/indexeddb/create-db-on-write.test.ts[2m > [22mIndexedDB create on write[2m > [22maction is not creating a db[2m > [22mtest 'del'
[31m[1mAssertionError[22m: expected { name: 'fp.indexdb-z2ZLyg4LPg', …(1) } to be undefined[39m

Failure screenshot:
  - [2mgateway/indexeddb/__screenshots__/create-db-on-write.test.ts/IndexedDB-create-on-write-action-is-not-creating-a-db-test--del--1.png[22m

Compared values have no visual difference.

[36m [2m❯[22m gateway/indexeddb/create-db-on-write.test.ts:[2m101:64[22m[39m
    [90m 99| [39m        [35mconst[39m idbs [33m=[39m [35mawait[39m indexedDB[33m.[39m[34mdatabases[39m()[33m;[39m
    [90m100| [39m        [90m// console.log("idbs", idbs, dbName);[39m
    [90m101| [39m        [34mexpect[39m(idbs[33m.[39m[34mfind[39m((i) [33m=>[39m i[33m.[39mname [33m===[39m [32m`fp.[39m[36m${[39mdbName[36m}[39m[32m`[39m))[33m.[39mnot[33m.[39m[34mtoBeDefined[39m()[33m;[39m
    [90m   | [39m                                                               [31m^[39m
    [90m102| [39m      } [35mfinally[39m {
    [90m103| [39m        [35mawait[39m db[33m.[39m[34mdestroy[39m()[33m;[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/2]⎯[22m[39m

[41m[1m FAIL [22m[49m [30m[42m indexeddb (chromium) [49m[39m gateway/indexeddb/create-db-on-write.test.ts[2m > [22mIndexedDB create on write[2m > [22mcombine not and creating[2m > [22mnot creating 'del'
[31m[1mAssertionError[22m: expected { name: 'fp.indexdb-z2SKx9CJsc', …(1) } to be undefined[39m

Failure screenshot:
  - [2mgateway/indexeddb/__screenshots__/create-db-on-write.test.ts/IndexedDB-create-on-write-combine-not-and-creating-not-creating--del--1.png[22m

Compared values have no visual difference.

[36m [2m❯[22m gateway/indexeddb/create-db-on-write.test.ts:[2m130:64[22m[39m
    [90m128| [39m        [35mconst[39m idbs [33m=[39m [35mawait[39m indexedDB[33m.[39m[34mdatabases[39m()[33m;[39m
    [90m129| [39m        [90m// console.log("idbs", idbs, dbName);[39m
    [90m130| [39m        [34mexpect[39m(idbs[33m.[39m[34mfind[39m((i) [33m=>[39m i[33m.[39mname [33m===[39m [32m`fp.[39m[36m${[39mdbName[36m}[39m[32m`[39m))[33m.[39mnot[33m.[39m[34mtoBeDefined[39m()[33m;[39m
    [90m   | [39m                                                               [31m^[39m
    [90m131| [39m    })[33m;[39m
    [90m132| [39m    it[33m.[39m[34meach[39m(creating)([32m"creating $name"[39m[33m,[39m [35masync[39m (item) [33m=>[39m {

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/2]⎯[22m[39m

