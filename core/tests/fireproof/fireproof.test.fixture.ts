export const docs = [
  {
    _id: "02pkji8",
    type: "todo",
    title: "On the browser",
    listId: "0k23ulso",
    completed: false,
    createdAt: "2024-06-30T15:15:01.482Z",
  },
  { _id: "0k23ulso", type: "list", title: "Building Apps" },
  { _id: "1ctdt28g", type: "list", title: "Having Fun" },
  { _id: "294n3m2o", type: "list", title: "Getting Groceries" },
  {
    _id: "63eheg8",
    type: "todo",
    title: "Macadamia nut milk",
    listId: "294n3m2o",
    completed: false,
    createdAt: "2024-06-30T15:15:03.282Z",
  },
  {
    _id: "65e459g",
    type: "todo",
    title: "Fruit salad",
    listId: "294n3m2o",
    completed: false,
    createdAt: "2024-06-30T15:15:04.144Z",
  },
  {
    _id: "6icqlvg",
    type: "todo",
    title: "<PERSON>",
    listId: "294n3m2o",
    completed: false,
    createdAt: "2024-06-30T15:15:05.282Z",
  },
  {
    _id: "7terjo",
    type: "todo",
    title: "Rollerskating meetup",
    listId: "1ctdt28g",
    completed: false,
    createdAt: "2024-06-30T15:15:06.282Z",
  },
  {
    _id: "8vr6cc",
    type: "todo",
    title: "With or without Redux",
    listId: "0k23ulso",
    completed: false,
    createdAt: "2024-06-30T15:15:07.282Z",
  },
  {
    _id: "ck2sb08",
    type: "todo",
    title: "Write a sci-fi story with ChatGPT",
    listId: "1ctdt28g",
    completed: true,
    createdAt: "2024-06-30T15:15:08.282Z",
  },
  {
    _id: "dlkeeoo",
    type: "todo",
    title: "Avocado toast",
    listId: "294n3m2o",
    completed: false,
    createdAt: "2024-06-30T15:15:09.282Z",
  },
  {
    _id: "f9i8tx7nxswo",
    type: "todo",
    title: "nice",
    listId: "0k23ulso",
    completed: true,
    createdAt: "2024-06-30T15:15:01.482Z",
  },
  {
    _id: "fkgjmbg",
    type: "todo",
    title: "Automatic replication and versioning",
    listId: "0k23ulso",
    completed: true,
    createdAt: "2024-06-30T15:15:10.282Z",
  },
  {
    _id: "fks8hig",
    type: "todo",
    title: "Motorcycle ride",
    listId: "1ctdt28g",
    completed: false,
    createdAt: "2024-06-30T15:15:11.282Z",
  },
  {
    _id: "hf60rf",
    type: "todo",
    title: "Sourdough bread",
    listId: "294n3m2o",
    completed: true,
    createdAt: "2024-06-30T15:15:12.282Z",
  },
  {
    _id: "n219un",
    type: "todo",
    title: "Login components",
    listId: "0k23ulso",
    completed: true,
    createdAt: "2024-06-30T15:15:13.282Z",
  },
  {
    _id: "phr936g",
    type: "todo",
    title: "Coffee",
    listId: "294n3m2o",
    completed: false,
    createdAt: "2024-06-30T15:15:14.282Z",
  },
  {
    _id: "vcj2fv8",
    type: "todo",
    title: "On the phone",
    listId: "0k23ulso",
    completed: false,
    createdAt: "2024-06-30T15:15:15.282Z",
  },
  {
    _id: "vqmbes8",
    type: "todo",
    title: "GraphQL queries",
    listId: "0k23ulso",
    completed: false,
    createdAt: "2024-06-30T15:15:16.282Z",
  },
];
