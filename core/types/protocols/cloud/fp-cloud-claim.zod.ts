import { z } from "zod";
// Zod schemas
export const V1TenantClaimSchema = z.object({
  id: z.string(),
  role: z.enum(["admin", "owner", "member"]),
});

export type V1TenantClaim = z.infer<typeof V1TenantClaimSchema>;

export const V1LedgerClaimSchema = z.object({
  id: z.string(),
  role: z.enum(["admin", "owner", "member"]),
  right: z.enum(["read", "write"]),
});

export type LedgerClaim = z.infer<typeof V1LedgerClaimSchema>;

export const TenantLedgerSchema = z.object({
  tenant: z.string(),
  ledger: z.string(),
});

export type TenantLedger = z.infer<typeof TenantLedgerSchema>;

export const FPCloudClaimSchema = z.object({
  // JWTPayload fields (common JWT claims)
  iss: z.string().optional(), // issuer
  sub: z.string().optional(), // subject
  aud: z.union([z.string(), z.array(z.string())]).optional(), // audience
  exp: z.number().optional(), // expiration time
  nbf: z.number().optional(), // not before
  iat: z.number().optional(), // issued at
  jti: z.string().optional(), // JWT ID
  
  // FPCloudClaim specific fields
  userId: z.string(),
  email: z.string().email(),
  nickname: z.string().optional(),
  provider: z.enum(["github", "google"]).optional(),
  created: z.date(),
  tenants: z.array(V1TenantClaimSchema),
  ledgers: z.array(V1LedgerClaimSchema),
  selected: TenantLedgerSchema,
});

// Type inference from schema
export type FPCloudClaim = z.infer<typeof FPCloudClaimSchema>;
