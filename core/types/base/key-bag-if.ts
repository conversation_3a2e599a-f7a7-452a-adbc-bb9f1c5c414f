import { Logger, URI, Result } from "@adviser/cement";
import { KeysByFingerprint } from "@fireproof/core-types-blockstore";
import { KeyBagRuntime } from "./types.js";

export interface KeyBagIf {
  readonly logger: Logger;
  readonly rt: KeyBagRuntime;

  subtleKey(materialStrOrUint8: string | Uint8Array): Promise<CryptoKey>;

  ensureKeyFromUrl(url: URI, keyFactory: () => string): Promise<Result<URI>>;
  // flush(): Promise<void>;

  getNamed<PERSON>ey(name: string, failIfNotFound?: boolean, material?: string | Uint8Array): Promise<Result<KeysByFingerprint>>;
}
