{"name": "@fireproof/core-types-runtime", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "main": "./index.js", "scripts": {"build": "core-cli tsc", "pack": "core-cli build --doPack", "publish": "core-cli build"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/vendor": "workspace:0.0.0", "multiformats": "^13.3.7"}}