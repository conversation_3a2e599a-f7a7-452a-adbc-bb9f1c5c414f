{"name": "@fireproof/core-base", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "main": "./index.js", "scripts": {"build": "core-cli tsc", "pack": "core-cli build --doPack", "publish": "core-cli build"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/core-blockstore": "workspace:0.0.0", "@fireproof/core-keybag": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/core-types-blockstore": "workspace:0.0.0", "@fireproof/core-types-protocols-cloud": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "@ipld/dag-cbor": "^9.2.4", "@web3-storage/pail": "^0.6.2", "charwise": "^3.0.1", "prolly-trees": "^1.0.4"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@types/node": "^24.1.0"}}