Scope: 30 of 31 workspace projects
cli pack$ echo skip
cloud/backend/cf-d1 pack$ echo cloud need not to pack
core/base pack$ core-cli build --doPack
cloud/backend/base pack$ echo cloud need not to pack
cli pack: skip
cloud/backend/cf-d1 pack: cloud need not to pack
cli pack: Done
cloud/backend/base pack: cloud need not to pack
cloud/backend/cf-d1 pack: Done
core/types/base pack$ core-cli build --doPack
cloud/backend/base pack: Done
core/types/blockstore pack$ core-cli build --doPack
core/types/base pack: $ cd ./dist
core/types/blockstore pack: $ cd ./dist
core/base pack: $ cd ./dist
core/types/base pack: $ pnpm run build
core/types/blockstore pack: $ pnpm run build
core/base pack: $ pnpm run build
core/base pack: > @fireproof/core-base@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/base/dist
core/base pack: > core-cli tsc
core/types/blockstore pack: > @fireproof/core-types-blockstore@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/types/blockstore/dist
core/types/blockstore pack: > core-cli tsc
core/types/base pack: > @fireproof/core-types-base@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/types/base/dist
core/types/base pack: > core-cli tsc
core/types/base pack: Using typescript: tsgo
core/base pack: Using typescript: tsgo
core/types/blockstore pack: Using typescript: tsgo
core/types/base pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/types/blockstore pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/base pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/types/base pack: 📦  @fireproof/core-types-base@0.0.0-smoke-0ff5d3d0-**********
core/types/base pack: Tarball Contents
core/types/base pack: index
core/types/base pack: index.d.ts
core/types/base pack: index.js
core/types/base pack: index.js.map
core/types/base pack: index.ts
core/types/base pack: indexer.d.ts
core/types/base pack: indexer.js
core/types/base pack: indexer.js.map
core/types/base pack: indexer.ts
core/types/base pack: key-bag-if.d.ts
core/types/base pack: key-bag-if.js
core/types/base pack: key-bag-if.js.map
core/types/base pack: key-bag-if.ts
core/types/base pack: LICENSE.md
core/types/base pack: package.json
core/types/base pack: prolly-trees.d.ts
core/types/base pack: README.md
core/types/base pack: tsconfig.json
core/types/base pack: types.d.ts
core/types/base pack: types.js
core/types/base pack: types.js.map
core/types/base pack: types.ts
core/types/base pack: Tarball Details
core/types/base pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-types-base.tgz
core/types/blockstore pack: 📦  @fireproof/core-types-blockstore@0.0.0-smoke-0ff5d3d0-**********
core/types/blockstore pack: Tarball Contents
core/types/blockstore pack: commit-queue-if.d.ts
core/types/blockstore pack: commit-queue-if.js
core/types/blockstore pack: commit-queue-if.js.map
core/types/blockstore pack: commit-queue-if.ts
core/types/blockstore pack: fp-envelope-serialize.d.ts
core/types/blockstore pack: fp-envelope-serialize.js
core/types/blockstore pack: fp-envelope-serialize.js.map
core/types/blockstore pack: fp-envelope-serialize.ts
core/types/blockstore pack: fp-envelope.d.ts
core/types/blockstore pack: fp-envelope.js
core/types/blockstore pack: fp-envelope.js.map
core/types/blockstore pack: fp-envelope.ts
core/types/blockstore pack: gateway.d.ts
core/types/blockstore pack: gateway.js
core/types/blockstore pack: gateway.js.map
core/types/blockstore pack: gateway.ts
core/types/blockstore pack: index.d.ts
core/types/blockstore pack: index.js
core/types/blockstore pack: index.js.map
core/types/blockstore pack: index.ts
core/types/blockstore pack: LICENSE.md
core/types/blockstore pack: package.json
core/types/blockstore pack: README.md
core/types/blockstore pack: serde-gateway.d.ts
core/types/blockstore pack: serde-gateway.js
core/types/blockstore pack: serde-gateway.js.map
core/types/blockstore pack: serde-gateway.ts
core/types/blockstore pack: task-manager-if.d.ts
core/types/blockstore pack: task-manager-if.js
core/types/blockstore pack: task-manager-if.js.map
core/types/blockstore pack: task-manager-if.ts
core/types/blockstore pack: tsconfig.json
core/types/blockstore pack: types.d.ts
core/types/blockstore pack: types.js
core/types/blockstore pack: types.js.map
core/types/blockstore pack: types.ts
core/types/blockstore pack: Tarball Details
core/types/blockstore pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-types-blockstore.tgz
core/types/base pack: Done
core/types/blockstore pack: Done
core/base pack: 📦  @fireproof/core-base@0.0.0-smoke-0ff5d3d0-**********
core/base pack: Tarball Contents
core/base pack: apply-head-queue.d.ts
core/base pack: apply-head-queue.js
core/base pack: apply-head-queue.js.map
core/base pack: apply-head-queue.ts
core/base pack: bundle-not-impl.d.ts
core/base pack: bundle-not-impl.js
core/base pack: bundle-not-impl.js.map
core/base pack: bundle-not-impl.ts
core/base pack: compact-strategies.d.ts
core/base pack: compact-strategies.js
core/base pack: compact-strategies.js.map
core/base pack: compact-strategies.ts
core/base pack: crdt-clock.d.ts
core/base pack: crdt-clock.js
core/base pack: crdt-clock.js.map
core/base pack: crdt-clock.ts
core/base pack: crdt-helpers.d.ts
core/base pack: crdt-helpers.js
core/base pack: crdt-helpers.js.map
core/base pack: crdt-helpers.ts
core/base pack: crdt.d.ts
core/base pack: crdt.js
core/base pack: crdt.js.map
core/base pack: crdt.ts
core/base pack: database.d.ts
core/base pack: database.js
core/base pack: database.js.map
core/base pack: database.ts
core/base pack: index.d.ts
core/base pack: index.js
core/base pack: index.js.map
core/base pack: index.ts
core/base pack: indexer-helpers.d.ts
core/base pack: indexer-helpers.js
core/base pack: indexer-helpers.js.map
core/base pack: indexer-helpers.ts
core/base pack: indexer.d.ts
core/base pack: indexer.js
core/base pack: indexer.js.map
core/base pack: indexer.ts
core/base pack: ledger.d.ts
core/base pack: ledger.js
core/base pack: ledger.js.map
core/base pack: ledger.ts
core/base pack: LICENSE.md
core/base pack: package.json
core/base pack: README.md
core/base pack: tsconfig.json
core/base pack: version.d.ts
core/base pack: version.js
core/base pack: version.js.map
core/base pack: version.ts
core/base pack: write-queue.d.ts
core/base pack: write-queue.js
core/base pack: write-queue.js.map
core/base pack: write-queue.ts
core/base pack: Tarball Details
core/base pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-base.tgz
core/base pack: Done
vendor pack$ core-cli build --doPack --noTsconfig
vendor pack: $ cd ./dist
vendor pack: $ pnpm run build
vendor pack: > @fireproof/vendor@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/vendor/dist
vendor pack: > echo done
vendor pack: done
vendor pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
vendor pack: 📦  @fireproof/vendor@0.0.0-smoke-0ff5d3d0-**********
vendor pack: Tarball Contents
vendor pack: LICENSE.md
vendor pack: merge-package.ts
vendor pack: p-limit/index.d.ts
vendor pack: p-limit/index.js
vendor pack: p-limit/index.test-d.ts
vendor pack: p-limit/license
vendor pack: p-limit/package.json
vendor pack: p-limit/readme.md
vendor pack: p-limit/test.js
vendor pack: package.json
vendor pack: README.md
vendor pack: to-esm-transform.ts
vendor pack: Tarball Details
vendor pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-vendor.tgz
vendor pack: Done
core/core pack$ core-cli build --doPack
core/gateways/file-deno pack$ core-cli build --doPack
core/gateways/file-node pack$ core-cli build --doPack
core/types/protocols/cloud pack$ core-cli build --doPack
core/core pack: $ cd ./dist
core/gateways/file-deno pack: $ cd ./dist
core/gateways/file-node pack: $ cd ./dist
core/types/protocols/cloud pack: $ cd ./dist
core/core pack: $ pnpm run build
core/gateways/file-deno pack: $ pnpm run build
core/gateways/file-node pack: $ pnpm run build
core/types/protocols/cloud pack: $ pnpm run build
core/types/protocols/cloud pack: > @fireproof/core-types-protocols-cloud@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/types/protocols/cloud/dist
core/types/protocols/cloud pack: > core-cli tsc
core/gateways/file-deno pack: > @fireproof/core-gateways-file-deno@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/file-deno/dist
core/gateways/file-deno pack: > core-cli tsc
core/core pack: > @fireproof/core@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/core/dist
core/core pack: > core-cli tsc
core/gateways/file-node pack: > @fireproof/core-gateways-file-node@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/file-node/dist
core/gateways/file-node pack: > core-cli tsc
core/gateways/file-deno pack: Using typescript: tsgo
core/types/protocols/cloud pack: Using typescript: tsgo
core/gateways/file-node pack: Using typescript: tsgo
core/core pack: Using typescript: tsgo
core/gateways/file-node pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/types/protocols/cloud pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/file-deno pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/core pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/file-node pack: 📦  @fireproof/core-gateways-file-node@0.0.0-smoke-0ff5d3d0-**********
core/gateways/file-node pack: Tarball Contents
core/gateways/file-node pack: get-sys-file-system.d.ts
core/gateways/file-node pack: get-sys-file-system.js
core/gateways/file-node pack: get-sys-file-system.js.map
core/gateways/file-node pack: get-sys-file-system.ts
core/gateways/file-node pack: index.d.ts
core/gateways/file-node pack: index.js
core/gateways/file-node pack: index.js.map
core/gateways/file-node pack: index.ts
core/gateways/file-node pack: LICENSE.md
core/gateways/file-node pack: node-filesystem.d.ts
core/gateways/file-node pack: node-filesystem.js
core/gateways/file-node pack: node-filesystem.js.map
core/gateways/file-node pack: node-filesystem.ts
core/gateways/file-node pack: package.json
core/gateways/file-node pack: README.md
core/gateways/file-node pack: to-array-buffer.d.ts
core/gateways/file-node pack: to-array-buffer.js
core/gateways/file-node pack: to-array-buffer.js.map
core/gateways/file-node pack: to-array-buffer.ts
core/gateways/file-node pack: tsconfig.json
core/gateways/file-node pack: Tarball Details
core/gateways/file-node pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-file-node.tgz
core/gateways/file-deno pack: 📦  @fireproof/core-gateways-file-deno@0.0.0-smoke-0ff5d3d0-**********
core/gateways/file-deno pack: Tarball Contents
core/gateways/file-deno pack: deno-filesystem.d.ts
core/gateways/file-deno pack: deno-filesystem.js
core/gateways/file-deno pack: deno-filesystem.js.map
core/gateways/file-deno pack: deno-filesystem.ts
core/gateways/file-deno pack: get-sys-file-system.d.ts
core/gateways/file-deno pack: get-sys-file-system.js
core/gateways/file-deno pack: get-sys-file-system.js.map
core/gateways/file-deno pack: get-sys-file-system.ts
core/gateways/file-deno pack: index.d.ts
core/gateways/file-deno pack: index.js
core/gateways/file-deno pack: index.js.map
core/gateways/file-deno pack: index.ts
core/gateways/file-deno pack: LICENSE.md
core/gateways/file-deno pack: package.json
core/gateways/file-deno pack: README.md
core/gateways/file-deno pack: tsconfig.json
core/gateways/file-deno pack: Tarball Details
core/gateways/file-deno pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-file-deno.tgz
core/types/protocols/cloud pack: 📦  @fireproof/core-types-protocols-cloud@0.0.0-smoke-0ff5d3d0-**********
core/types/protocols/cloud pack: Tarball Contents
core/types/protocols/cloud pack: gateway-control.d.ts
core/types/protocols/cloud pack: gateway-control.js
core/types/protocols/cloud pack: gateway-control.js.map
core/types/protocols/cloud pack: gateway-control.ts
core/types/protocols/cloud pack: index.d.ts
core/types/protocols/cloud pack: index.js
core/types/protocols/cloud pack: index.js.map
core/types/protocols/cloud pack: index.ts
core/types/protocols/cloud pack: LICENSE.md
core/types/protocols/cloud pack: msg-types-data.d.ts
core/types/protocols/cloud pack: msg-types-data.js
core/types/protocols/cloud pack: msg-types-data.js.map
core/types/protocols/cloud pack: msg-types-data.ts
core/types/protocols/cloud pack: msg-types-meta.d.ts
core/types/protocols/cloud pack: msg-types-meta.js
core/types/protocols/cloud pack: msg-types-meta.js.map
core/types/protocols/cloud pack: msg-types-meta.ts
core/types/protocols/cloud pack: msg-types-wal.d.ts
core/types/protocols/cloud pack: msg-types-wal.js
core/types/protocols/cloud pack: msg-types-wal.js.map
core/types/protocols/cloud pack: msg-types-wal.ts
core/types/protocols/cloud pack: msg-types.d.ts
core/types/protocols/cloud pack: msg-types.js
core/types/protocols/cloud pack: msg-types.js.map
core/types/protocols/cloud pack: msg-types.ts
core/types/protocols/cloud pack: package.json
core/types/protocols/cloud pack: README.md
core/types/protocols/cloud pack: tsconfig.json
core/types/protocols/cloud pack: Tarball Details
core/types/protocols/cloud pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-types-protocols-cloud.tgz
core/gateways/file-node pack: Done
core/types/runtime pack$ core-cli build --doPack
core/gateways/file-deno pack: Done
core/types/protocols/cloud pack: Done
core/core pack: 📦  @fireproof/core@0.0.0-smoke-0ff5d3d0-**********
core/core pack: Tarball Contents
core/core pack: index.d.ts
core/core pack: index.js
core/core pack: index.js.map
core/core pack: index.ts
core/core pack: LICENSE.md
core/core pack: package.json
core/core pack: README.md
core/core pack: tsconfig.json
core/core pack: Tarball Details
core/core pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core.tgz
core/core pack: Done
core/types/runtime pack: $ cd ./dist
core/types/runtime pack: $ pnpm run build
core/types/runtime pack: > @fireproof/core-types-runtime@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/types/runtime/dist
core/types/runtime pack: > core-cli tsc
core/types/runtime pack: Using typescript: tsgo
core/types/runtime pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/types/runtime pack: 📦  @fireproof/core-types-runtime@0.0.0-smoke-0ff5d3d0-**********
core/types/runtime pack: Tarball Contents
core/types/runtime pack: codec-interface.d.ts
core/types/runtime pack: codec-interface.js
core/types/runtime pack: codec-interface.js.map
core/types/runtime pack: codec-interface.ts
core/types/runtime pack: index.d.ts
core/types/runtime pack: index.js
core/types/runtime pack: index.js.map
core/types/runtime pack: index.ts
core/types/runtime pack: LICENSE.md
core/types/runtime pack: package.json
core/types/runtime pack: README.md
core/types/runtime pack: tsconfig.json
core/types/runtime pack: Tarball Details
core/types/runtime pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-types-runtime.tgz
core/types/runtime pack: Done
core/runtime pack$ core-cli build --doPack
core/runtime pack: $ cd ./dist
core/runtime pack: $ pnpm run build
core/runtime pack: > @fireproof/core-runtime@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/runtime/dist
core/runtime pack: > core-cli tsc
core/runtime pack: Using typescript: tsgo
core/runtime pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/runtime pack: 📦  @fireproof/core-runtime@0.0.0-smoke-0ff5d3d0-**********
core/runtime pack: Tarball Contents
core/runtime pack: async-block-encode.d.ts
core/runtime pack: async-block-encode.js
core/runtime pack: async-block-encode.js.map
core/runtime pack: async-block-encode.ts
core/runtime pack: commit-queue.d.ts
core/runtime pack: commit-queue.js
core/runtime pack: commit-queue.js.map
core/runtime pack: commit-queue.ts
core/runtime pack: files.d.ts
core/runtime pack: files.js
core/runtime pack: files.js.map
core/runtime pack: files.ts
core/runtime pack: index.d.ts
core/runtime pack: index.js
core/runtime pack: index.js.map
core/runtime pack: index.ts
core/runtime pack: keyed-crypto.d.ts
core/runtime pack: keyed-crypto.js
core/runtime pack: keyed-crypto.js.map
core/runtime pack: keyed-crypto.ts
core/runtime pack: LICENSE.md
core/runtime pack: memory-sys-container.d.ts
core/runtime pack: memory-sys-container.js
core/runtime pack: memory-sys-container.js.map
core/runtime pack: memory-sys-container.ts
core/runtime pack: package.json
core/runtime pack: README.md
core/runtime pack: register-compact-strategy.d.ts
core/runtime pack: register-compact-strategy.js
core/runtime pack: register-compact-strategy.js.map
core/runtime pack: register-compact-strategy.ts
core/runtime pack: sts-service/index.d.ts
core/runtime pack: sts-service/index.d.ts.map
core/runtime pack: sts-service/index.js
core/runtime pack: sts-service/index.js.map
core/runtime pack: sts-service/index.ts
core/runtime pack: sys-container.d.ts
core/runtime pack: sys-container.js
core/runtime pack: sys-container.js.map
core/runtime pack: sys-container.ts
core/runtime pack: task-manager.d.ts
core/runtime pack: task-manager.js
core/runtime pack: task-manager.js.map
core/runtime pack: task-manager.ts
core/runtime pack: tsconfig.json
core/runtime pack: utils.d.ts
core/runtime pack: utils.d.ts.map
core/runtime pack: utils.js
core/runtime pack: utils.js.map
core/runtime pack: utils.ts
core/runtime pack: Tarball Details
core/runtime pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-runtime.tgz
core/runtime pack: Done
core/protocols/dashboard pack$ core-cli build --doPack
core/gateways/base pack$ core-cli build --doPack
core/protocols/cloud pack$ core-cli build --doPack
core/protocols/dashboard pack: $ cd ./dist
core/protocols/cloud pack: $ cd ./dist
core/gateways/base pack: $ cd ./dist
core/protocols/dashboard pack: $ pnpm run build
core/protocols/cloud pack: $ pnpm run build
core/gateways/base pack: $ pnpm run build
core/gateways/base pack: > @fireproof/core-gateways-base@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/base/dist
core/gateways/base pack: > core-cli tsc
core/protocols/dashboard pack: > @fireproof/core-protocols-dashboard@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/protocols/dashboard/dist
core/protocols/dashboard pack: > core-cli tsc
core/protocols/cloud pack: > @fireproof/core-protocols-cloud@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/protocols/cloud/dist
core/protocols/cloud pack: > core-cli tsc
core/protocols/dashboard pack: Using typescript: tsgo
core/gateways/base pack: Using typescript: tsgo
core/protocols/cloud pack: Using typescript: tsgo
core/protocols/dashboard pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/base pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/protocols/cloud pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/protocols/dashboard pack: 📦  @fireproof/core-protocols-dashboard@0.0.0-smoke-0ff5d3d0-**********
core/protocols/dashboard pack: Tarball Contents
core/protocols/dashboard pack: index.d.ts
core/protocols/dashboard pack: index.js
core/protocols/dashboard pack: index.js.map
core/protocols/dashboard pack: index.ts
core/protocols/dashboard pack: LICENSE.md
core/protocols/dashboard pack: msg-api.d.ts
core/protocols/dashboard pack: msg-api.js
core/protocols/dashboard pack: msg-api.js.map
core/protocols/dashboard pack: msg-api.ts
core/protocols/dashboard pack: msg-is.d.ts
core/protocols/dashboard pack: msg-is.js
core/protocols/dashboard pack: msg-is.js.map
core/protocols/dashboard pack: msg-is.ts
core/protocols/dashboard pack: msg-types.d.ts
core/protocols/dashboard pack: msg-types.js
core/protocols/dashboard pack: msg-types.js.map
core/protocols/dashboard pack: msg-types.ts
core/protocols/dashboard pack: package.json
core/protocols/dashboard pack: README.md
core/protocols/dashboard pack: tsconfig.json
core/protocols/dashboard pack: Tarball Details
core/protocols/dashboard pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-protocols-dashboard.tgz
core/gateways/base pack: 📦  @fireproof/core-gateways-base@0.0.0-smoke-0ff5d3d0-**********
core/gateways/base pack: Tarball Contents
core/gateways/base pack: def-serde-gateway.d.ts
core/gateways/base pack: def-serde-gateway.js
core/gateways/base pack: def-serde-gateway.js.map
core/gateways/base pack: def-serde-gateway.ts
core/gateways/base pack: fp-envelope-serialize.d.ts
core/gateways/base pack: fp-envelope-serialize.js
core/gateways/base pack: fp-envelope-serialize.js.map
core/gateways/base pack: fp-envelope-serialize.ts
core/gateways/base pack: index.d.ts
core/gateways/base pack: index.js
core/gateways/base pack: index.js.map
core/gateways/base pack: index.ts
core/gateways/base pack: indexeddb-version.d.ts
core/gateways/base pack: indexeddb-version.js
core/gateways/base pack: indexeddb-version.js.map
core/gateways/base pack: indexeddb-version.ts
core/gateways/base pack: interceptor-gateway.d.ts
core/gateways/base pack: interceptor-gateway.js
core/gateways/base pack: interceptor-gateway.js.map
core/gateways/base pack: interceptor-gateway.ts
core/gateways/base pack: LICENSE.md
core/gateways/base pack: meta-key-hack.d.ts
core/gateways/base pack: meta-key-hack.js
core/gateways/base pack: meta-key-hack.js.map
core/gateways/base pack: meta-key-hack.ts
core/gateways/base pack: package.json
core/gateways/base pack: README.md
core/gateways/base pack: tsconfig.json
core/gateways/base pack: uri-interceptor.d.ts
core/gateways/base pack: uri-interceptor.js
core/gateways/base pack: uri-interceptor.js.map
core/gateways/base pack: uri-interceptor.ts
core/gateways/base pack: utils.d.ts
core/gateways/base pack: utils.js
core/gateways/base pack: utils.js.map
core/gateways/base pack: utils.ts
core/gateways/base pack: Tarball Details
core/gateways/base pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-base.tgz
core/protocols/cloud pack: 📦  @fireproof/core-protocols-cloud@0.0.0-smoke-0ff5d3d0-**********
core/protocols/cloud pack: Tarball Contents
core/protocols/cloud pack: http-connection.d.ts
core/protocols/cloud pack: http-connection.js
core/protocols/cloud pack: http-connection.js.map
core/protocols/cloud pack: http-connection.ts
core/protocols/cloud pack: index.d.ts
core/protocols/cloud pack: index.js
core/protocols/cloud pack: index.js.map
core/protocols/cloud pack: index.ts
core/protocols/cloud pack: LICENSE.md
core/protocols/cloud pack: msg-raw-connection-base.d.ts
core/protocols/cloud pack: msg-raw-connection-base.js
core/protocols/cloud pack: msg-raw-connection-base.js.map
core/protocols/cloud pack: msg-raw-connection-base.ts
core/protocols/cloud pack: msger.d.ts
core/protocols/cloud pack: msger.js
core/protocols/cloud pack: msger.js.map
core/protocols/cloud pack: msger.ts
core/protocols/cloud pack: package.json
core/protocols/cloud pack: README.md
core/protocols/cloud pack: tsconfig.json
core/protocols/cloud pack: ws-connection.d.ts
core/protocols/cloud pack: ws-connection.js
core/protocols/cloud pack: ws-connection.js.map
core/protocols/cloud pack: ws-connection.ts
core/protocols/cloud pack: Tarball Details
core/protocols/cloud pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-protocols-cloud.tgz
core/protocols/dashboard pack: Done
core/gateways/base pack: Done
core/protocols/cloud pack: Done
core/gateways/cloud pack$ core-cli build --doPack
core/gateways/file pack$ core-cli build --doPack
core/gateways/memory pack$ core-cli build --doPack
core/gateways/indexeddb pack$ core-cli build --doPack
core/gateways/memory pack: $ cd ./dist
core/gateways/cloud pack: $ cd ./dist
core/gateways/indexeddb pack: $ cd ./dist
core/gateways/file pack: $ cd ./dist
core/gateways/memory pack: $ pnpm run build
core/gateways/cloud pack: $ pnpm run build
core/gateways/indexeddb pack: $ pnpm run build
core/gateways/file pack: $ pnpm run build
core/gateways/indexeddb pack: > @fireproof/core-gateways-indexeddb@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/indexeddb/dist
core/gateways/indexeddb pack: > core-cli tsc
core/gateways/memory pack: > @fireproof/core-gateways-memory@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/memory/dist
core/gateways/memory pack: > core-cli tsc
core/gateways/cloud pack: > @fireproof/core-gateways-cloud@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/cloud/dist
core/gateways/cloud pack: > core-cli tsc
core/gateways/file pack: > @fireproof/core-gateways-file@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/gateways/file/dist
core/gateways/file pack: > core-cli tsc
core/gateways/indexeddb pack: Using typescript: tsgo
core/gateways/file pack: Using typescript: tsgo
core/gateways/cloud pack: Using typescript: tsgo
core/gateways/memory pack: Using typescript: tsgo
core/gateways/memory pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/indexeddb pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/file pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/cloud pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/gateways/memory pack: 📦  @fireproof/core-gateways-memory@0.0.0-smoke-0ff5d3d0-**********
core/gateways/memory pack: Tarball Contents
core/gateways/memory pack: gateway.d.ts
core/gateways/memory pack: gateway.js
core/gateways/memory pack: gateway.js.map
core/gateways/memory pack: gateway.ts
core/gateways/memory pack: index.d.ts
core/gateways/memory pack: index.js
core/gateways/memory pack: index.js.map
core/gateways/memory pack: index.ts
core/gateways/memory pack: LICENSE.md
core/gateways/memory pack: package.json
core/gateways/memory pack: README.md
core/gateways/memory pack: tsconfig.json
core/gateways/memory pack: version.d.ts
core/gateways/memory pack: version.js
core/gateways/memory pack: version.js.map
core/gateways/memory pack: version.ts
core/gateways/memory pack: Tarball Details
core/gateways/memory pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-memory.tgz
core/gateways/indexeddb pack: 📦  @fireproof/core-gateways-indexeddb@0.0.0-smoke-0ff5d3d0-**********
core/gateways/indexeddb pack: Tarball Contents
core/gateways/indexeddb pack: dummy-idb.d.ts
core/gateways/indexeddb pack: dummy-idb.js
core/gateways/indexeddb pack: dummy-idb.js.map
core/gateways/indexeddb pack: dummy-idb.ts
core/gateways/indexeddb pack: gateway-impl.d.ts
core/gateways/indexeddb pack: gateway-impl.js
core/gateways/indexeddb pack: gateway-impl.js.map
core/gateways/indexeddb pack: gateway-impl.ts
core/gateways/indexeddb pack: index.d.ts
core/gateways/indexeddb pack: index.js
core/gateways/indexeddb pack: index.js.map
core/gateways/indexeddb pack: index.ts
core/gateways/indexeddb pack: key-bag-indexeddb.d.ts
core/gateways/indexeddb pack: key-bag-indexeddb.js
core/gateways/indexeddb pack: key-bag-indexeddb.js.map
core/gateways/indexeddb pack: key-bag-indexeddb.ts
core/gateways/indexeddb pack: LICENSE.md
core/gateways/indexeddb pack: package.json
core/gateways/indexeddb pack: README.md
core/gateways/indexeddb pack: tsconfig.json
core/gateways/indexeddb pack: Tarball Details
core/gateways/indexeddb pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-indexeddb.tgz
core/gateways/file pack: 📦  @fireproof/core-gateways-file@0.0.0-smoke-0ff5d3d0-**********
core/gateways/file pack: Tarball Contents
core/gateways/file pack: gateway-impl.d.ts
core/gateways/file pack: gateway-impl.js
core/gateways/file pack: gateway-impl.js.map
core/gateways/file pack: gateway-impl.ts
core/gateways/file pack: index.d.ts
core/gateways/file pack: index.js
core/gateways/file pack: index.js.map
core/gateways/file pack: index.ts
core/gateways/file pack: key-bag-file.d.ts
core/gateways/file pack: key-bag-file.js
core/gateways/file pack: key-bag-file.js.map
core/gateways/file pack: key-bag-file.ts
core/gateways/file pack: LICENSE.md
core/gateways/file pack: package.json
core/gateways/file pack: README.md
core/gateways/file pack: sys-file-system-factory.d.ts
core/gateways/file pack: sys-file-system-factory.js
core/gateways/file pack: sys-file-system-factory.js.map
core/gateways/file pack: sys-file-system-factory.ts
core/gateways/file pack: tsconfig.json
core/gateways/file pack: version.d.ts
core/gateways/file pack: version.js
core/gateways/file pack: version.js.map
core/gateways/file pack: version.ts
core/gateways/file pack: Tarball Details
core/gateways/file pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-file.tgz
core/gateways/cloud pack: 📦  @fireproof/core-gateways-cloud@0.0.0-smoke-0ff5d3d0-**********
core/gateways/cloud pack: Tarball Contents
core/gateways/cloud pack: gateway.d.ts
core/gateways/cloud pack: gateway.js
core/gateways/cloud pack: gateway.js.map
core/gateways/cloud pack: gateway.ts
core/gateways/cloud pack: index.d.ts
core/gateways/cloud pack: index.js
core/gateways/cloud pack: index.js.map
core/gateways/cloud pack: index.ts
core/gateways/cloud pack: LICENSE.md
core/gateways/cloud pack: package.json
core/gateways/cloud pack: README.md
core/gateways/cloud pack: to-cloud.d.ts
core/gateways/cloud pack: to-cloud.js
core/gateways/cloud pack: to-cloud.js.map
core/gateways/cloud pack: to-cloud.ts
core/gateways/cloud pack: tsconfig.json
core/gateways/cloud pack: Tarball Details
core/gateways/cloud pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-gateways-cloud.tgz
core/gateways/memory pack: Done
core/gateways/indexeddb pack: Done
core/gateways/file pack: Done
core/gateways/cloud pack: Done
core/keybag pack$ core-cli build --doPack
core/keybag pack: $ cd ./dist
core/keybag pack: $ pnpm run build
core/keybag pack: > @fireproof/core-keybag@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/keybag/dist
core/keybag pack: > core-cli tsc
core/keybag pack: Using typescript: tsgo
core/keybag pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/keybag pack: 📦  @fireproof/core-keybag@0.0.0-smoke-0ff5d3d0-**********
core/keybag pack: Tarball Contents
core/keybag pack: index.d.ts
core/keybag pack: index.js
core/keybag pack: index.js.map
core/keybag pack: index.ts
core/keybag pack: key-bag-memory.d.ts
core/keybag pack: key-bag-memory.js
core/keybag pack: key-bag-memory.js.map
core/keybag pack: key-bag-memory.ts
core/keybag pack: key-bag.d.ts
core/keybag pack: key-bag.js
core/keybag pack: key-bag.js.map
core/keybag pack: key-bag.ts
core/keybag pack: LICENSE.md
core/keybag pack: package.json
core/keybag pack: README.md
core/keybag pack: tsconfig.json
core/keybag pack: Tarball Details
core/keybag pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-keybag.tgz
core/keybag pack: Done
core/blockstore pack$ core-cli build --doPack
use-fireproof/pkg pack$ core-cli build --doPack
core/blockstore pack: $ cd ./dist
core/blockstore pack: $ pnpm run build
use-fireproof/pkg pack: $ cd ./dist
use-fireproof/pkg pack: $ pnpm run build
use-fireproof/pkg pack: > use-fireproof@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/use-fireproof/pkg/dist
use-fireproof/pkg pack: > core-cli tsc
core/blockstore pack: > @fireproof/core-blockstore@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/blockstore/dist
core/blockstore pack: > core-cli tsc
core/blockstore pack: Using typescript: tsgo
use-fireproof/pkg pack: Using typescript: tsgo
core/blockstore pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
use-fireproof/pkg pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/blockstore pack: 📦  @fireproof/core-blockstore@0.0.0-smoke-0ff5d3d0-**********
core/blockstore pack: Tarball Contents
core/blockstore pack: attachable-store.d.ts
core/blockstore pack: attachable-store.js
core/blockstore pack: attachable-store.js.map
core/blockstore pack: attachable-store.ts
core/blockstore pack: commitor.d.ts
core/blockstore pack: commitor.js
core/blockstore pack: commitor.js.map
core/blockstore pack: commitor.ts
core/blockstore pack: connect-raw.d.ts
core/blockstore pack: connect-raw.js
core/blockstore pack: connect-raw.js.map
core/blockstore pack: connect-raw.ts
core/blockstore pack: connection-base.d.ts
core/blockstore pack: connection-base.js
core/blockstore pack: connection-base.js.map
core/blockstore pack: connection-base.ts
core/blockstore pack: fragment-gateway.ts-off
core/blockstore pack: index.d.ts
core/blockstore pack: index.js
core/blockstore pack: index.js.map
core/blockstore pack: index.ts
core/blockstore pack: LICENSE.md
core/blockstore pack: loader-helpers.d.ts
core/blockstore pack: loader-helpers.js
core/blockstore pack: loader-helpers.js.map
core/blockstore pack: loader-helpers.ts
core/blockstore pack: loader.d.ts
core/blockstore pack: loader.js
core/blockstore pack: loader.js.map
core/blockstore pack: loader.ts
core/blockstore pack: package.json
core/blockstore pack: README.md
core/blockstore pack: register-store-protocol.d.ts
core/blockstore pack: register-store-protocol.js
core/blockstore pack: register-store-protocol.js.map
core/blockstore pack: register-store-protocol.ts
core/blockstore pack: store-factory.d.ts
core/blockstore pack: store-factory.js
core/blockstore pack: store-factory.js.map
core/blockstore pack: store-factory.ts
core/blockstore pack: store.d.ts
core/blockstore pack: store.js
core/blockstore pack: store.js.map
core/blockstore pack: store.ts
core/blockstore pack: transaction.d.ts
core/blockstore pack: transaction.js
core/blockstore pack: transaction.js.map
core/blockstore pack: transaction.ts
core/blockstore pack: tsconfig.json
core/blockstore pack: Tarball Details
core/blockstore pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-blockstore.tgz
core/blockstore pack: Done
use-fireproof/pkg pack: 📦  use-fireproof@0.0.0-smoke-0ff5d3d0-**********
use-fireproof/pkg pack: Tarball Contents
use-fireproof/pkg pack: iframe-strategy.d.ts
use-fireproof/pkg pack: iframe-strategy.js
use-fireproof/pkg pack: iframe-strategy.js.map
use-fireproof/pkg pack: iframe-strategy.ts
use-fireproof/pkg pack: index.d.ts
use-fireproof/pkg pack: index.js
use-fireproof/pkg pack: index.js.map
use-fireproof/pkg pack: index.ts
use-fireproof/pkg pack: LICENSE.md
use-fireproof/pkg pack: package.json
use-fireproof/pkg pack: react/img-file.d.ts
use-fireproof/pkg pack: react/img-file.js
use-fireproof/pkg pack: react/img-file.js.map
use-fireproof/pkg pack: react/img-file.ts
use-fireproof/pkg pack: react/index.d.ts
use-fireproof/pkg pack: react/index.js
use-fireproof/pkg pack: react/index.js.map
use-fireproof/pkg pack: react/index.ts
use-fireproof/pkg pack: react/types.d.ts
use-fireproof/pkg pack: react/types.js
use-fireproof/pkg pack: react/types.js.map
use-fireproof/pkg pack: react/types.ts
use-fireproof/pkg pack: react/use-all-docs.d.ts
use-fireproof/pkg pack: react/use-all-docs.js
use-fireproof/pkg pack: react/use-all-docs.js.map
use-fireproof/pkg pack: react/use-all-docs.ts
use-fireproof/pkg pack: react/use-attach.d.ts
use-fireproof/pkg pack: react/use-attach.js
use-fireproof/pkg pack: react/use-attach.js.map
use-fireproof/pkg pack: react/use-attach.ts
use-fireproof/pkg pack: react/use-changes.d.ts
use-fireproof/pkg pack: react/use-changes.js
use-fireproof/pkg pack: react/use-changes.js.map
use-fireproof/pkg pack: react/use-changes.ts
use-fireproof/pkg pack: react/use-document.d.ts
use-fireproof/pkg pack: react/use-document.js
use-fireproof/pkg pack: react/use-document.js.map
use-fireproof/pkg pack: react/use-document.ts
use-fireproof/pkg pack: react/use-fireproof.d.ts
use-fireproof/pkg pack: react/use-fireproof.js
use-fireproof/pkg pack: react/use-fireproof.js.map
use-fireproof/pkg pack: react/use-fireproof.ts
use-fireproof/pkg pack: react/use-live-query.d.ts
use-fireproof/pkg pack: react/use-live-query.js
use-fireproof/pkg pack: react/use-live-query.js.map
use-fireproof/pkg pack: react/use-live-query.ts
use-fireproof/pkg pack: README.md
use-fireproof/pkg pack: redirect-strategy.d.ts
use-fireproof/pkg pack: redirect-strategy.js
use-fireproof/pkg pack: redirect-strategy.js.map
use-fireproof/pkg pack: redirect-strategy.ts
use-fireproof/pkg pack: tsconfig.json
use-fireproof/pkg pack: Tarball Details
use-fireproof/pkg pack: /Users/<USER>/Software/fproof/fireproof/dist/use-fireproof.tgz
use-fireproof/pkg pack: Done
cloud/3rd-party pack$ echo cloud need not to pack
core/tests pack$ core-cli build --doPack
cloud/todo-app pack$ echo cloud need not to pack
cloud/base pack$ echo cloud need not to pack
cloud/3rd-party pack: cloud need not to pack
cloud/3rd-party pack: Done
dashboard pack$ echo dashboard need not to pack
cloud/todo-app pack: cloud need not to pack
cloud/todo-app pack: Done
use-fireproof/tests pack$ core-cli build --doPack
cloud/base pack: cloud need not to pack
cloud/base pack: Done
dashboard pack: dashboard need not to pack
dashboard pack: Done
use-fireproof/tests pack: $ cd ./dist
use-fireproof/tests pack: $ pnpm run build
core/tests pack: $ cd ./dist
core/tests pack: $ pnpm run build
use-fireproof/tests pack: > use-fireproof-test@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/use-fireproof/tests/dist
use-fireproof/tests pack: > core-cli tsc
core/tests pack: > @fireproof/core-test@0.0.0-smoke-0ff5d3d0-********** build /Users/<USER>/Software/fproof/fireproof/core/tests/dist
core/tests pack: > core-cli tsc
use-fireproof/tests pack: Using typescript: tsgo
core/tests pack: Using typescript: tsgo
use-fireproof/tests pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
core/tests pack: $ pnpm pack --out "/Users/<USER>/Software/fproof/fireproof/dist/%s.tgz"
use-fireproof/tests pack: 📦  use-fireproof-test@0.0.0-smoke-0ff5d3d0-**********
use-fireproof/tests pack: Tarball Contents
use-fireproof/tests pack: dynamic-db-and-attach.test.d.ts
use-fireproof/tests pack: dynamic-db-and-attach.test.js
use-fireproof/tests pack: dynamic-db-and-attach.test.js.map
use-fireproof/tests pack: dynamic-db-and-attach.test.ts
use-fireproof/tests pack: img-file.test.d.ts
use-fireproof/tests pack: img-file.test.js
use-fireproof/tests pack: img-file.test.js.map
use-fireproof/tests pack: img-file.test.tsx
use-fireproof/tests pack: LICENSE.md
use-fireproof/tests pack: package.json
use-fireproof/tests pack: README.md
use-fireproof/tests pack: tsconfig.json
use-fireproof/tests pack: use-all-docs.test.d.ts
use-fireproof/tests pack: use-all-docs.test.js
use-fireproof/tests pack: use-all-docs.test.js.map
use-fireproof/tests pack: use-all-docs.test.tsx
use-fireproof/tests pack: use-document-with-nonexistent-id.test.d.ts
use-fireproof/tests pack: use-document-with-nonexistent-id.test.js
use-fireproof/tests pack: use-document-with-nonexistent-id.test.js.map
use-fireproof/tests pack: use-document-with-nonexistent-id.test.tsx
use-fireproof/tests pack: use-fireproof-db-switch.test.d.ts
use-fireproof/tests pack: use-fireproof-db-switch.test.js
use-fireproof/tests pack: use-fireproof-db-switch.test.js.map
use-fireproof/tests pack: use-fireproof-db-switch.test.tsx
use-fireproof/tests pack: use-fireproof-stability.test.d.ts
use-fireproof/tests pack: use-fireproof-stability.test.js
use-fireproof/tests pack: use-fireproof-stability.test.js.map
use-fireproof/tests pack: use-fireproof-stability.test.tsx
use-fireproof/tests pack: use-fireproof.test.d.ts
use-fireproof/tests pack: use-fireproof.test.js
use-fireproof/tests pack: use-fireproof.test.js.map
use-fireproof/tests pack: use-fireproof.test.tsx
use-fireproof/tests pack: vitest.config.d.ts
use-fireproof/tests pack: vitest.config.js
use-fireproof/tests pack: vitest.config.js.map
use-fireproof/tests pack: vitest.config.ts
use-fireproof/tests pack: Tarball Details
use-fireproof/tests pack: /Users/<USER>/Software/fproof/fireproof/dist/use-fireproof-test.tgz
use-fireproof/tests pack: Done
core/tests pack: 📦  @fireproof/core-test@0.0.0-smoke-0ff5d3d0-**********
core/tests pack: Tarball Contents
core/tests pack: blockstore/fp-envelope.test.ts-off
core/tests pack: blockstore/fragment-gateway.test.ts-off
core/tests pack: blockstore/interceptor-gateway.test.d.ts
core/tests pack: blockstore/interceptor-gateway.test.js
core/tests pack: blockstore/interceptor-gateway.test.js.map
core/tests pack: blockstore/interceptor-gateway.test.ts
core/tests pack: blockstore/keyed-crypto-indexeddb-file.test.d.ts
core/tests pack: blockstore/keyed-crypto-indexeddb-file.test.js
core/tests pack: blockstore/keyed-crypto-indexeddb-file.test.js.map
core/tests pack: blockstore/keyed-crypto-indexeddb-file.test.ts
core/tests pack: blockstore/keyed-crypto.test.d.ts
core/tests pack: blockstore/keyed-crypto.test.js
core/tests pack: blockstore/keyed-crypto.test.js.map
core/tests pack: blockstore/keyed-crypto.test.ts
core/tests pack: blockstore/loader.test.d.ts
core/tests pack: blockstore/loader.test.js
core/tests pack: blockstore/loader.test.js.map
core/tests pack: blockstore/loader.test.ts
core/tests pack: blockstore/standalone.test.d.ts
core/tests pack: blockstore/standalone.test.js
core/tests pack: blockstore/standalone.test.js.map
core/tests pack: blockstore/standalone.test.ts
core/tests pack: blockstore/store.test.d.ts
core/tests pack: blockstore/store.test.js
core/tests pack: blockstore/store.test.js.map
core/tests pack: blockstore/store.test.ts
core/tests pack: blockstore/transaction.test.d.ts
core/tests pack: blockstore/transaction.test.js
core/tests pack: blockstore/transaction.test.js.map
core/tests pack: blockstore/transaction.test.ts
core/tests pack: fireproof/all-gateway.test.d.ts
core/tests pack: fireproof/all-gateway.test.js
core/tests pack: fireproof/all-gateway.test.js.map
core/tests pack: fireproof/all-gateway.test.ts
core/tests pack: fireproof/attachable.test.d.ts
core/tests pack: fireproof/attachable.test.js
core/tests pack: fireproof/attachable.test.js.map
core/tests pack: fireproof/attachable.test.ts
core/tests pack: fireproof/cars/bafkreidxwt2nhvbl4fnqfw3ctlt6zbrir4kqwmjo5im6rf4q5si27kgo2i.car
core/tests pack: fireproof/cars/bafkreidxwt2nhvbl4fnqfw3ctlt6zbrir4kqwmjo5im6rf4q5si27kgo2i.d.ts
core/tests pack: fireproof/cars/bafkreidxwt2nhvbl4fnqfw3ctlt6zbrir4kqwmjo5im6rf4q5si27kgo2i.js
core/tests pack: fireproof/cars/bafkreidxwt2nhvbl4fnqfw3ctlt6zbrir4kqwmjo5im6rf4q5si27kgo2i.js.map
core/tests pack: fireproof/cars/bafkreidxwt2nhvbl4fnqfw3ctlt6zbrir4kqwmjo5im6rf4q5si27kgo2i.ts
core/tests pack: fireproof/charwise-boolean.test.d.ts
core/tests pack: fireproof/charwise-boolean.test.js
core/tests pack: fireproof/charwise-boolean.test.js.map
core/tests pack: fireproof/charwise-boolean.test.ts
core/tests pack: fireproof/compact-strategy.test.d.ts
core/tests pack: fireproof/compact-strategy.test.js
core/tests pack: fireproof/compact-strategy.test.js.map
core/tests pack: fireproof/compact-strategy.test.ts
core/tests pack: fireproof/concurrent.test.d.ts
core/tests pack: fireproof/concurrent.test.js
core/tests pack: fireproof/concurrent.test.js.map
core/tests pack: fireproof/concurrent.test.ts
core/tests pack: fireproof/crdt.test.d.ts
core/tests pack: fireproof/crdt.test.js
core/tests pack: fireproof/crdt.test.js.map
core/tests pack: fireproof/crdt.test.ts
core/tests pack: fireproof/database.test.d.ts
core/tests pack: fireproof/database.test.js
core/tests pack: fireproof/database.test.js.map
core/tests pack: fireproof/database.test.ts
core/tests pack: fireproof/deleted-docs-handling.test.d.ts
core/tests pack: fireproof/deleted-docs-handling.test.js
core/tests pack: fireproof/deleted-docs-handling.test.js.map
core/tests pack: fireproof/deleted-docs-handling.test.ts
core/tests pack: fireproof/fireproof.test.d.ts
core/tests pack: fireproof/fireproof.test.fixture.d.ts
core/tests pack: fireproof/fireproof.test.fixture.js
core/tests pack: fireproof/fireproof.test.fixture.js.map
core/tests pack: fireproof/fireproof.test.fixture.ts
core/tests pack: fireproof/fireproof.test.js
core/tests pack: fireproof/fireproof.test.js.map
core/tests pack: fireproof/fireproof.test.ts
core/tests pack: fireproof/hello.test.d.ts
core/tests pack: fireproof/hello.test.js
core/tests pack: fireproof/hello.test.js.map
core/tests pack: fireproof/hello.test.ts
core/tests pack: fireproof/indexer.test.d.ts
core/tests pack: fireproof/indexer.test.js
core/tests pack: fireproof/indexer.test.js.map
core/tests pack: fireproof/indexer.test.ts
core/tests pack: fireproof/multiple-ledger.test.d.ts
core/tests pack: fireproof/multiple-ledger.test.js
core/tests pack: fireproof/multiple-ledger.test.js.map
core/tests pack: fireproof/multiple-ledger.test.ts
core/tests pack: fireproof/query-docs.test.d.ts
core/tests pack: fireproof/query-docs.test.js
core/tests pack: fireproof/query-docs.test.js.map
core/tests pack: fireproof/query-docs.test.ts
core/tests pack: fireproof/query-limit-issue.test.d.ts
core/tests pack: fireproof/query-limit-issue.test.js
core/tests pack: fireproof/query-limit-issue.test.js.map
core/tests pack: fireproof/query-limit-issue.test.ts
core/tests pack: fireproof/query-property-inconsistency.test.d.ts
core/tests pack: fireproof/query-property-inconsistency.test.js
core/tests pack: fireproof/query-property-inconsistency.test.js.map
core/tests pack: fireproof/query-property-inconsistency.test.ts
core/tests pack: fireproof/query-result-properties.test.d.ts
core/tests pack: fireproof/query-result-properties.test.js
core/tests pack: fireproof/query-result-properties.test.js.map
core/tests pack: fireproof/query-result-properties.test.ts
core/tests pack: fireproof/stable-cid.test.d.ts
core/tests pack: fireproof/stable-cid.test.js
core/tests pack: fireproof/stable-cid.test.js.map
core/tests pack: fireproof/stable-cid.test.ts
core/tests pack: fireproof/utils.test.d.ts
core/tests pack: fireproof/utils.test.js
core/tests pack: fireproof/utils.test.js.map
core/tests pack: fireproof/utils.test.ts
core/tests pack: gateway/file/loader-config.test.d.ts
core/tests pack: gateway/file/loader-config.test.js
core/tests pack: gateway/file/loader-config.test.js.map
core/tests pack: gateway/file/loader-config.test.ts
core/tests pack: gateway/indexeddb/create-db-on-write.test.d.ts
core/tests pack: gateway/indexeddb/create-db-on-write.test.js
core/tests pack: gateway/indexeddb/create-db-on-write.test.js.map
core/tests pack: gateway/indexeddb/create-db-on-write.test.ts
core/tests pack: gateway/indexeddb/loader-config.test.d.ts
core/tests pack: gateway/indexeddb/loader-config.test.js
core/tests pack: gateway/indexeddb/loader-config.test.js.map
core/tests pack: gateway/indexeddb/loader-config.test.ts
core/tests pack: git
core/tests pack: global-setup.d.ts
core/tests pack: global-setup.js
core/tests pack: global-setup.js.map
core/tests pack: global-setup.ts
core/tests pack: helpers.d.ts
core/tests pack: helpers.js
core/tests pack: helpers.js.map
core/tests pack: helpers.ts
core/tests pack: LICENSE.md
core/tests pack: package.json
core/tests pack: protocols/cloud/msger.test.d.ts
core/tests pack: protocols/cloud/msger.test.js
core/tests pack: protocols/cloud/msger.test.js.map
core/tests pack: protocols/cloud/msger.test.ts
core/tests pack: README.md
core/tests pack: runtime/fp-envelope-serialize.test.d.ts
core/tests pack: runtime/fp-envelope-serialize.test.js
core/tests pack: runtime/fp-envelope-serialize.test.js.map
core/tests pack: runtime/fp-envelope-serialize.test.ts
core/tests pack: runtime/key-bag.test.d.ts
core/tests pack: runtime/key-bag.test.js
core/tests pack: runtime/key-bag.test.js.map
core/tests pack: runtime/key-bag.test.ts
core/tests pack: runtime/meta-key-hack.test.d.ts
core/tests pack: runtime/meta-key-hack.test.js
core/tests pack: runtime/meta-key-hack.test.js.map
core/tests pack: runtime/meta-key-hack.test.ts
core/tests pack: setup.file.d.ts
core/tests pack: setup.file.js
core/tests pack: setup.file.js.map
core/tests pack: setup.file.ts
core/tests pack: setup.indexeddb.d.ts
core/tests pack: setup.indexeddb.js
core/tests pack: setup.indexeddb.js.map
core/tests pack: setup.indexeddb.ts
core/tests pack: setup.memory.d.ts
core/tests pack: setup.memory.js
core/tests pack: setup.memory.js.map
core/tests pack: setup.memory.ts
core/tests pack: status
core/tests pack: tsconfig.json
core/tests pack: vitest.config.d.ts
core/tests pack: vitest.config.js
core/tests pack: vitest.config.js.map
core/tests pack: vitest.config.ts
core/tests pack: vitest.file.config.d.ts
core/tests pack: vitest.file.config.js
core/tests pack: vitest.file.config.js.map
core/tests pack: vitest.file.config.ts
core/tests pack: vitest.indexeddb.config.d.ts
core/tests pack: vitest.indexeddb.config.js
core/tests pack: vitest.indexeddb.config.js.map
core/tests pack: vitest.indexeddb.config.ts
core/tests pack: vitest.memory.config.d.ts
core/tests pack: vitest.memory.config.js
core/tests pack: vitest.memory.config.js.map
core/tests pack: vitest.memory.config.ts
core/tests pack: x
core/tests pack: Tarball Details
core/tests pack: /Users/<USER>/Software/fproof/fireproof/dist/fireproof-core-test.tgz
core/tests pack: Done
cloud/backend/node pack$ echo cloud need no pack
cloud/backend/node pack: cloud need no pack
cloud/backend/node pack: Done
