{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "core-cli tsc", "lint": "eslint .", "preview": "vite preview", "pack": "core-cli build --doPack", "publish": "core-cli build"}, "dependencies": {"@fireproof/core": "0.20.0-dev-preview-63", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "use-fireproof": "0.20.0-dev-preview-63", "@adviser/cement": "^0.4.21", "@fireproof/vendor": "^3.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "@fireproof/core-cli": "workspace:0.0.0"}}