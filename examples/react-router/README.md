# React Router Example with Fireproof

A bare-bones `react-router` example built using [bolt.new](https://bolt.new) and powered by [Fireproof](https://fireproof.storage).

## Live Demo

Live Demo is hosted at [stackblitz](https://stackblitz.com/edit/sb1-szgwhw4b?file=src%2Fpages%2FHome.tsx)

## Installation

```bash
npm install
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Author

Created by [@drmikecrowe](https://github.com/drmikecrowe)
