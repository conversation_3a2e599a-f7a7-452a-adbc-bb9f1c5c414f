diff --git a/README.md b/README.md
deleted file mode 100644
index bd69a4d3d8e4f7359acb7336281c9898ef10dc69..0000000000000000000000000000000000000000
diff --git a/api.js b/api.js
index 24f82ccc03dde45b0c23cb2422345c22dc200a16..ea31782603804531e9ddc2c2efc836229bf613e6 100644
--- a/api.js
+++ b/api.js
@@ -3124,11 +3124,11 @@ var require_hanji = __commonJS({
     "use strict";
     var __awaiter = exports2 && exports2.__awaiter || function(thisArg, _arguments, P, generator) {
       function adopt(value) {
-        return value instanceof P ? value : new P(function(resolve) {
-          resolve(value);
+        return value instanceof P ? value : new P(function(resolve2) {
+          resolve2(value);
         });
       }
-      return new (P || (P = Promise))(function(resolve, reject) {
+      return new (P || (P = Promise))(function(resolve2, reject) {
         function fulfilled(value) {
           try {
             step(generator.next(value));
@@ -3144,7 +3144,7 @@ var require_hanji = __commonJS({
           }
         }
         function step(result) {
-          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
+          result.done ? resolve2(result.value) : adopt(result.value).then(fulfilled, rejected);
         }
         step((generator = generator.apply(thisArg, _arguments || [])).next());
       });
@@ -3218,14 +3218,14 @@ var require_hanji = __commonJS({
     };
     exports2.SelectState = SelectState3;
     var deferred = () => {
-      let resolve;
+      let resolve2;
       let reject;
       const promise = new Promise((res, rej) => {
-        resolve = res;
+        resolve2 = res;
         reject = rej;
       });
       return {
-        resolve,
+        resolve: resolve2,
         reject,
         promise
       };
@@ -3275,8 +3275,8 @@ var require_hanji = __commonJS({
         };
         this.stdin.on("keypress", keypress);
         this.view.attach(this);
-        const { resolve, promise } = (0, exports2.deferred)();
-        this.resolve = resolve;
+        const { resolve: resolve2, promise } = (0, exports2.deferred)();
+        this.resolve = resolve2;
         this.promise = promise;
         this.renderFunc = (0, lodash_throttle_1.default)((str) => {
           this.stdout.write(str);
@@ -22841,7 +22841,7 @@ var init_sql = __esm({
         return new SQL([new StringChunk(str)]);
       }
       sql2.raw = raw;
-      function join(chunks, separator) {
+      function join2(chunks, separator) {
         const result = [];
         for (const [i, chunk] of chunks.entries()) {
           if (i > 0 && separator !== void 0) {
@@ -22851,7 +22851,7 @@ var init_sql = __esm({
         }
         return new SQL(result);
       }
-      sql2.join = join;
+      sql2.join = join2;
       function identifier(value) {
         return new Name(value);
       }
@@ -27750,7 +27750,7 @@ var init_select2 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -28532,7 +28532,7 @@ var init_update = __esm({
       createJoin(joinType) {
         return (table6, on) => {
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (typeof on === "function") {
@@ -28624,10 +28624,10 @@ var init_update = __esm({
               const fromFields = this.getTableLikeFields(this.config.from);
               fields[tableName] = fromFields;
             }
-            for (const join of this.config.joins) {
-              const tableName2 = getTableLikeName(join.table);
-              if (typeof tableName2 === "string" && !is(join.table, SQL)) {
-                const fromFields = this.getTableLikeFields(join.table);
+            for (const join2 of this.config.joins) {
+              const tableName2 = getTableLikeName(join2.table);
+              if (typeof tableName2 === "string" && !is(join2.table, SQL)) {
+                const fromFields = this.getTableLikeFields(join2.table);
                 fields[tableName2] = fromFields;
               }
             }
@@ -33509,7 +33509,7 @@ var init_select3 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -34157,7 +34157,7 @@ var init_update2 = __esm({
       createJoin(joinType) {
         return (table6, on) => {
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (typeof on === "function") {
@@ -39222,7 +39222,7 @@ var init_select4 = __esm({
         return (table6, on, onIndex) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -44503,7 +44503,7 @@ var init_select5 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -45962,12 +45962,16 @@ __export(sqliteImports_exports, {
   prepareFromExports: () => prepareFromExports2,
   prepareFromSqliteImports: () => prepareFromSqliteImports
 });
-var prepareFromExports2, prepareFromSqliteImports;
+var esbuild, import_promises, import_path, prepareFromExports2, prepareFromSqliteImports;
 var init_sqliteImports = __esm({
   "src/serializer/sqliteImports.ts"() {
     "use strict";
+    init_source();
     init_dist();
     init_sqlite_core();
+    esbuild = __toESM(require("esbuild"));
+    import_promises = require("fs/promises");
+    import_path = require("path");
     init_utils9();
     prepareFromExports2 = (exports2) => {
       const tables = [];
@@ -45987,13 +45991,20 @@ var init_sqliteImports = __esm({
       const tables = [];
       const views = [];
       const { unregister } = await safeRegister();
-      for (let i = 0; i < imports.length; i++) {
-        const it = imports[i];
-        const i0 = require(`${it}`);
-        const prepared = prepareFromExports2(i0);
-        tables.push(...prepared.tables);
-        views.push(...prepared.views);
-      }
+      const outDir = await (0, import_promises.mkdtemp)(".drizzle-kit.sqlite-imports-");
+      let outFile = (0, import_path.resolve)((0, import_path.join)(outDir, "schema.js"));
+      console.log(source_default.grey(`Reading schema files '${JSON.stringify(imports)}'`));
+      const res = esbuild.buildSync({
+        entryPoints: imports,
+        bundle: true,
+        format: "esm",
+        outfile: outFile
+      });
+      const i0 = require(outFile);
+      const prepared = prepareFromExports2(i0);
+      await (0, import_promises.rm)(outDir, { recursive: true });
+      tables.push(...prepared.tables);
+      views.push(...prepared.views);
       unregister();
       return { tables: Array.from(new Set(tables)), views };
     };
diff --git a/api.mjs b/api.mjs
index e95ffbb6d3b90b01cbfdaaee13e9ed5a6a979a56..1660a9de5dcd39155027e9debfc4eb9cab7f8866 100644
--- a/api.mjs
+++ b/api.mjs
@@ -3129,11 +3129,11 @@ var require_hanji = __commonJS({
     "use strict";
     var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
       function adopt(value) {
-        return value instanceof P ? value : new P(function(resolve) {
-          resolve(value);
+        return value instanceof P ? value : new P(function(resolve2) {
+          resolve2(value);
         });
       }
-      return new (P || (P = Promise))(function(resolve, reject) {
+      return new (P || (P = Promise))(function(resolve2, reject) {
         function fulfilled(value) {
           try {
             step(generator.next(value));
@@ -3149,7 +3149,7 @@ var require_hanji = __commonJS({
           }
         }
         function step(result) {
-          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
+          result.done ? resolve2(result.value) : adopt(result.value).then(fulfilled, rejected);
         }
         step((generator = generator.apply(thisArg, _arguments || [])).next());
       });
@@ -3223,14 +3223,14 @@ var require_hanji = __commonJS({
     };
     exports.SelectState = SelectState3;
     var deferred = () => {
-      let resolve;
+      let resolve2;
       let reject;
       const promise = new Promise((res, rej) => {
-        resolve = res;
+        resolve2 = res;
         reject = rej;
       });
       return {
-        resolve,
+        resolve: resolve2,
         reject,
         promise
       };
@@ -3280,8 +3280,8 @@ var require_hanji = __commonJS({
         };
         this.stdin.on("keypress", keypress);
         this.view.attach(this);
-        const { resolve, promise } = (0, exports.deferred)();
-        this.resolve = resolve;
+        const { resolve: resolve2, promise } = (0, exports.deferred)();
+        this.resolve = resolve2;
         this.promise = promise;
         this.renderFunc = (0, lodash_throttle_1.default)((str) => {
           this.stdout.write(str);
@@ -22846,7 +22846,7 @@ var init_sql = __esm({
         return new SQL([new StringChunk(str)]);
       }
       sql2.raw = raw;
-      function join(chunks, separator) {
+      function join2(chunks, separator) {
         const result = [];
         for (const [i, chunk] of chunks.entries()) {
           if (i > 0 && separator !== void 0) {
@@ -22856,7 +22856,7 @@ var init_sql = __esm({
         }
         return new SQL(result);
       }
-      sql2.join = join;
+      sql2.join = join2;
       function identifier(value) {
         return new Name(value);
       }
@@ -27755,7 +27755,7 @@ var init_select2 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -28537,7 +28537,7 @@ var init_update = __esm({
       createJoin(joinType) {
         return (table6, on) => {
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (typeof on === "function") {
@@ -28629,10 +28629,10 @@ var init_update = __esm({
               const fromFields = this.getTableLikeFields(this.config.from);
               fields[tableName] = fromFields;
             }
-            for (const join of this.config.joins) {
-              const tableName2 = getTableLikeName(join.table);
-              if (typeof tableName2 === "string" && !is(join.table, SQL)) {
-                const fromFields = this.getTableLikeFields(join.table);
+            for (const join2 of this.config.joins) {
+              const tableName2 = getTableLikeName(join2.table);
+              if (typeof tableName2 === "string" && !is(join2.table, SQL)) {
+                const fromFields = this.getTableLikeFields(join2.table);
                 fields[tableName2] = fromFields;
               }
             }
@@ -33514,7 +33514,7 @@ var init_select3 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -34162,7 +34162,7 @@ var init_update2 = __esm({
       createJoin(joinType) {
         return (table6, on) => {
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (typeof on === "function") {
@@ -39227,7 +39227,7 @@ var init_select4 = __esm({
         return (table6, on, onIndex) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -44508,7 +44508,7 @@ var init_select5 = __esm({
         return (table6, on) => {
           const baseTableName = this.tableName;
           const tableName = getTableLikeName(table6);
-          if (typeof tableName === "string" && this.config.joins?.some((join) => join.alias === tableName)) {
+          if (typeof tableName === "string" && this.config.joins?.some((join2) => join2.alias === tableName)) {
             throw new Error(`Alias "${tableName}" is already used in this query`);
           }
           if (!this.isPartialSelect) {
@@ -45967,10 +45967,14 @@ __export(sqliteImports_exports, {
   prepareFromExports: () => prepareFromExports2,
   prepareFromSqliteImports: () => prepareFromSqliteImports
 });
+import * as esbuild from "esbuild";
+import { mkdtemp, rm } from "fs/promises";
+import { join, resolve } from "path";
 var prepareFromExports2, prepareFromSqliteImports;
 var init_sqliteImports = __esm({
   "src/serializer/sqliteImports.ts"() {
     "use strict";
+    init_source();
     init_dist();
     init_sqlite_core();
     init_utils9();
@@ -45992,13 +45996,20 @@ var init_sqliteImports = __esm({
       const tables = [];
       const views = [];
       const { unregister } = await safeRegister();
-      for (let i = 0; i < imports.length; i++) {
-        const it = imports[i];
-        const i0 = __require(`${it}`);
-        const prepared = prepareFromExports2(i0);
-        tables.push(...prepared.tables);
-        views.push(...prepared.views);
-      }
+      const outDir = await mkdtemp(".drizzle-kit.sqlite-imports-");
+      let outFile = resolve(join(outDir, "schema.js"));
+      console.log(source_default.grey(`Reading schema files '${JSON.stringify(imports)}'`));
+      const res = esbuild.buildSync({
+        entryPoints: imports,
+        bundle: true,
+        format: "esm",
+        outfile: outFile
+      });
+      const i0 = __require(outFile);
+      const prepared = prepareFromExports2(i0);
+      await rm(outDir, { recursive: true });
+      tables.push(...prepared.tables);
+      views.push(...prepared.views);
       unregister();
       return { tables: Array.from(new Set(tables)), views };
     };
diff --git a/bin.cjs b/bin.cjs
old mode 100755
new mode 100644
index 959405ffb553981776dac860ff9a5c2853ceef70..a3b9b475867f6207ec019f2f82b91a243ad5c4e0
--- a/bin.cjs
+++ b/bin.cjs
@@ -1191,11 +1191,11 @@ var require_hanji = __commonJS({
     "use strict";
     var __awaiter3 = exports2 && exports2.__awaiter || function(thisArg, _arguments, P, generator) {
       function adopt(value) {
-        return value instanceof P ? value : new P(function(resolve2) {
-          resolve2(value);
+        return value instanceof P ? value : new P(function(resolve3) {
+          resolve3(value);
         });
       }
-      return new (P || (P = Promise))(function(resolve2, reject) {
+      return new (P || (P = Promise))(function(resolve3, reject) {
         function fulfilled(value) {
           try {
             step(generator.next(value));
@@ -1211,7 +1211,7 @@ var require_hanji = __commonJS({
           }
         }
         function step(result) {
-          result.done ? resolve2(result.value) : adopt(result.value).then(fulfilled, rejected);
+          result.done ? resolve3(result.value) : adopt(result.value).then(fulfilled, rejected);
         }
         step((generator = generator.apply(thisArg, _arguments || [])).next());
       });
@@ -1285,14 +1285,14 @@ var require_hanji = __commonJS({
     };
     exports2.SelectState = SelectState3;
     var deferred = () => {
-      let resolve2;
+      let resolve3;
       let reject;
       const promise = new Promise((res, rej) => {
-        resolve2 = res;
+        resolve3 = res;
         reject = rej;
       });
       return {
-        resolve: resolve2,
+        resolve: resolve3,
         reject,
         promise
       };
@@ -1342,8 +1342,8 @@ var require_hanji = __commonJS({
         };
         this.stdin.on("keypress", keypress);
         this.view.attach(this);
-        const { resolve: resolve2, promise } = (0, exports2.deferred)();
-        this.resolve = resolve2;
+        const { resolve: resolve3, promise } = (0, exports2.deferred)();
+        this.resolve = resolve3;
         this.promise = promise;
         this.renderFunc = (0, lodash_throttle_1.default)((str) => {
           this.stdout.write(str);
@@ -7868,7 +7868,7 @@ function unescapeSingleQuotes(str, ignoreFirstAndLastChar) {
   const regex = ignoreFirstAndLastChar ? /(?<!^)'(?!$)/g : /'/g;
   return str.replace(/''/g, "'").replace(regex, "\\'");
 }
-var import_fs, import_path, import_url, copy, objectValues, assertV1OutFolder, dryJournal, prepareOutFolder, validatorForDialect, validateWithReport, prepareMigrationFolder, prepareMigrationMeta, schemaRenameKey, tableRenameKey, columnRenameKey, normaliseSQLiteUrl, normalisePGliteUrl;
+var import_fs, import_path, import_url, copy, objectValues, assertV1OutFolder, dryJournal, prepareOutFolder, validatorForDialect, validateWithReport, prepareMigrationFolder, prepareMigrationMeta, schemaRenameKey, tableRenameKey, columnRenameKey, mkdirSQLiteUrl, normaliseSQLiteUrl, normalisePGliteUrl;
 var init_utils2 = __esm({
   "src/utils.ts"() {
     "use strict";
@@ -8050,7 +8050,14 @@ var init_utils2 = __esm({
       const out = schema6 ? `"${schema6}"."${table6}"."${column11}"` : `"${table6}"."${column11}"`;
       return out;
     };
+    mkdirSQLiteUrl = (it) => {
+      if (it.startsWith("file:")) {
+        it = it.substring(5);
+      }
+      (0, import_fs.mkdirSync)((0, import_path.dirname)(it), { recursive: true });
+    };
     normaliseSQLiteUrl = (it, type) => {
+      mkdirSQLiteUrl(it);
       if (type === "libsql") {
         if (it.startsWith("file:")) {
           return it;
@@ -14174,7 +14181,7 @@ var require_node2 = __commonJS({
       function _interopRequireDefault(obj) {
         return obj && obj.__esModule ? obj : { default: obj };
       }
-      function asyncGeneratorStep(gen, resolve2, reject, _next, _throw, key, arg) {
+      function asyncGeneratorStep(gen, resolve3, reject, _next, _throw, key, arg) {
         try {
           var info2 = gen[key](arg);
           var value = info2.value;
@@ -14183,7 +14190,7 @@ var require_node2 = __commonJS({
           return;
         }
         if (info2.done) {
-          resolve2(value);
+          resolve3(value);
         } else {
           Promise.resolve(value).then(_next, _throw);
         }
@@ -14191,31 +14198,31 @@ var require_node2 = __commonJS({
       function _asyncToGenerator(fn) {
         return function() {
           var self2 = this, args = arguments;
-          return new Promise(function(resolve2, reject) {
+          return new Promise(function(resolve3, reject) {
             var gen = fn.apply(self2, args);
             function _next(value) {
-              asyncGeneratorStep(gen, resolve2, reject, _next, _throw, "next", value);
+              asyncGeneratorStep(gen, resolve3, reject, _next, _throw, "next", value);
             }
             function _throw(err2) {
-              asyncGeneratorStep(gen, resolve2, reject, _next, _throw, "throw", err2);
+              asyncGeneratorStep(gen, resolve3, reject, _next, _throw, "throw", err2);
             }
             _next(void 0);
           });
         };
       }
-      var readFile2 = (fp) => new Promise((resolve2, reject) => {
+      var readFile2 = (fp) => new Promise((resolve3, reject) => {
         _fs.default.readFile(fp, "utf8", (err2, data) => {
           if (err2)
             return reject(err2);
-          resolve2(data);
+          resolve3(data);
         });
       });
       var readFileSync3 = (fp) => {
         return _fs.default.readFileSync(fp, "utf8");
       };
-      var pathExists = (fp) => new Promise((resolve2) => {
+      var pathExists = (fp) => new Promise((resolve3) => {
         _fs.default.access(fp, (err2) => {
-          resolve2(!err2);
+          resolve3(!err2);
         });
       });
       var pathExistsSync = _fs.default.existsSync;
@@ -19997,12 +20004,16 @@ __export(sqliteImports_exports, {
   prepareFromExports: () => prepareFromExports3,
   prepareFromSqliteImports: () => prepareFromSqliteImports
 });
-var import_drizzle_orm5, import_sqlite_core, prepareFromExports3, prepareFromSqliteImports;
+var import_drizzle_orm5, import_sqlite_core, esbuild, import_promises2, import_path4, prepareFromExports3, prepareFromSqliteImports;
 var init_sqliteImports = __esm({
   "src/serializer/sqliteImports.ts"() {
     "use strict";
+    init_source();
     import_drizzle_orm5 = require("drizzle-orm");
     import_sqlite_core = require("drizzle-orm/sqlite-core");
+    esbuild = __toESM(require("esbuild"));
+    import_promises2 = require("fs/promises");
+    import_path4 = require("path");
     init_utils3();
     prepareFromExports3 = (exports2) => {
       const tables = [];
@@ -20022,13 +20033,20 @@ var init_sqliteImports = __esm({
       const tables = [];
       const views = [];
       const { unregister } = await safeRegister();
-      for (let i2 = 0; i2 < imports.length; i2++) {
-        const it = imports[i2];
-        const i0 = require(`${it}`);
-        const prepared = prepareFromExports3(i0);
-        tables.push(...prepared.tables);
-        views.push(...prepared.views);
-      }
+      const outDir = await (0, import_promises2.mkdtemp)(".drizzle-kit.sqlite-imports-");
+      let outFile = (0, import_path4.resolve)((0, import_path4.join)(outDir, "schema.js"));
+      console.log(source_default.grey(`Reading schema files '${JSON.stringify(imports)}'`));
+      const res = esbuild.buildSync({
+        entryPoints: imports,
+        bundle: true,
+        format: "esm",
+        outfile: outFile
+      });
+      const i0 = require(outFile);
+      const prepared = prepareFromExports3(i0);
+      await (0, import_promises2.rm)(outDir, { recursive: true });
+      tables.push(...prepared.tables);
+      views.push(...prepared.views);
       unregister();
       return { tables: Array.from(new Set(tables)), views };
     };
@@ -21213,14 +21231,14 @@ The unique index ${source_default.underline.blue(
 });
 
 // src/serializer/index.ts
-var import_fs4, glob, import_path4, serializeMySql, serializePg, serializeSQLite, serializeSingleStore, prepareFilenames;
+var import_fs4, glob, import_path5, serializeMySql, serializePg, serializeSQLite, serializeSingleStore, prepareFilenames;
 var init_serializer = __esm({
   "src/serializer/index.ts"() {
     "use strict";
     init_source();
     import_fs4 = __toESM(require("fs"));
     glob = __toESM(require_glob());
-    import_path4 = __toESM(require("path"));
+    import_path5 = __toESM(require("path"));
     init_views();
     serializeMySql = async (path4, casing2) => {
       const filenames = prepareFilenames(path4);
@@ -21273,8 +21291,8 @@ ${filenames.join("\n")}
       const result = path4.reduce((result2, cur) => {
         const globbed = glob.sync(`${prefix2}${cur}`);
         globbed.forEach((it) => {
-          const fileName = import_fs4.default.lstatSync(it).isDirectory() ? null : import_path4.default.resolve(it);
-          const filenames = fileName ? [fileName] : import_fs4.default.readdirSync(it).map((file) => import_path4.default.join(import_path4.default.resolve(it), file));
+          const fileName = import_fs4.default.lstatSync(it).isDirectory() ? null : import_path5.default.resolve(it);
+          const filenames = fileName ? [fileName] : import_fs4.default.readdirSync(it).map((file) => import_path5.default.join(import_path5.default.resolve(it), file));
           filenames.filter((file) => !import_fs4.default.lstatSync(file).isDirectory()).forEach((file) => result2.add(file));
         });
         return result2;
@@ -33444,7 +33462,7 @@ __export(migrate_exports, {
   viewsResolver: () => viewsResolver,
   writeResult: () => writeResult
 });
-var import_fs6, import_hanji3, import_path5, schemasResolver, tablesResolver, viewsResolver, mySqlViewsResolver, sqliteViewsResolver, sequencesResolver, roleResolver, policyResolver, indPolicyResolver, enumsResolver, columnsResolver, prepareAndMigratePg, prepareAndExportPg, preparePgPush, prepareMySQLPush, prepareAndMigrateMysql, prepareSingleStorePush, prepareAndMigrateSingleStore, prepareAndExportSinglestore, prepareAndExportMysql, prepareAndMigrateSqlite, prepareAndExportSqlite, prepareAndMigrateLibSQL, prepareAndExportLibSQL, prepareSQLitePush, prepareLibSQLPush, promptColumnsConflicts, promptNamedConflict, promptNamedWithSchemasConflict, promptSchemasConflict, BREAKPOINT, writeResult, embeddedMigrations, prepareSnapshotFolderName, two;
+var import_fs6, import_hanji3, import_path6, schemasResolver, tablesResolver, viewsResolver, mySqlViewsResolver, sqliteViewsResolver, sequencesResolver, roleResolver, policyResolver, indPolicyResolver, enumsResolver, columnsResolver, prepareAndMigratePg, prepareAndExportPg, preparePgPush, prepareMySQLPush, prepareAndMigrateMysql, prepareSingleStorePush, prepareAndMigrateSingleStore, prepareAndExportSinglestore, prepareAndExportMysql, prepareAndMigrateSqlite, prepareAndExportSqlite, prepareAndMigrateLibSQL, prepareAndExportLibSQL, prepareSQLitePush, prepareLibSQLPush, promptColumnsConflicts, promptNamedConflict, promptNamedWithSchemasConflict, promptSchemasConflict, BREAKPOINT, writeResult, embeddedMigrations, prepareSnapshotFolderName, two;
 var init_migrate = __esm({
   "src/cli/commands/migrate.ts"() {
     "use strict";
@@ -33452,7 +33470,7 @@ var init_migrate = __esm({
     init_migrationPreparator();
     init_source();
     import_hanji3 = __toESM(require_hanji());
-    import_path5 = __toESM(require("path"));
+    import_path6 = __toESM(require("path"));
     init_singlestoreSchema();
     init_mysqlSchema();
     init_pgSchema();
@@ -34406,10 +34424,10 @@ var init_migrate = __esm({
       const { prefix: prefix2, tag } = prepareMigrationMetadata(idx, prefixMode, name);
       const toSave = JSON.parse(JSON.stringify(cur));
       toSave["_meta"] = _meta;
-      const metaFolderPath = (0, import_path5.join)(outFolder, "meta");
-      const metaJournal = (0, import_path5.join)(metaFolderPath, "_journal.json");
+      const metaFolderPath = (0, import_path6.join)(outFolder, "meta");
+      const metaJournal = (0, import_path6.join)(metaFolderPath, "_journal.json");
       import_fs6.default.writeFileSync(
-        (0, import_path5.join)(metaFolderPath, `${prefix2}_snapshot.json`),
+        (0, import_path6.join)(metaFolderPath, `${prefix2}_snapshot.json`),
         JSON.stringify(toSave, null, 2)
       );
       const sqlDelimiter = breakpoints ? BREAKPOINT : "\n";
@@ -34442,7 +34460,7 @@ ${sql}
         `[${source_default.green(
           "\u2713"
         )}] Your SQL migration file \u279C ${source_default.bold.underline.blue(
-          import_path5.default.join(`${outFolder}/${tag}.sql`)
+          import_path6.default.join(`${outFolder}/${tag}.sql`)
         )} \u{1F680}`
       );
     };
@@ -36616,8 +36634,8 @@ var require_ponyfill_es2018 = __commonJS({
         return new TypeError("Cannot " + name + " a stream using a released reader");
       }
       function defaultReaderClosedPromiseInitialize(reader) {
-        reader._closedPromise = newPromise((resolve2, reject) => {
-          reader._closedPromise_resolve = resolve2;
+        reader._closedPromise = newPromise((resolve3, reject) => {
+          reader._closedPromise_resolve = resolve3;
           reader._closedPromise_reject = reject;
         });
       }
@@ -36795,8 +36813,8 @@ var require_ponyfill_es2018 = __commonJS({
           }
           let resolvePromise;
           let rejectPromise;
-          const promise = newPromise((resolve2, reject) => {
-            resolvePromise = resolve2;
+          const promise = newPromise((resolve3, reject) => {
+            resolvePromise = resolve3;
             rejectPromise = reject;
           });
           const readRequest = {
@@ -36892,8 +36910,8 @@ var require_ponyfill_es2018 = __commonJS({
           }
           let resolvePromise;
           let rejectPromise;
-          const promise = newPromise((resolve2, reject) => {
-            resolvePromise = resolve2;
+          const promise = newPromise((resolve3, reject) => {
+            resolvePromise = resolve3;
             rejectPromise = reject;
           });
           const readRequest = {
@@ -37726,8 +37744,8 @@ var require_ponyfill_es2018 = __commonJS({
           }
           let resolvePromise;
           let rejectPromise;
-          const promise = newPromise((resolve2, reject) => {
-            resolvePromise = resolve2;
+          const promise = newPromise((resolve3, reject) => {
+            resolvePromise = resolve3;
             rejectPromise = reject;
           });
           const readIntoRequest = {
@@ -38024,10 +38042,10 @@ var require_ponyfill_es2018 = __commonJS({
           wasAlreadyErroring = true;
           reason = void 0;
         }
-        const promise = newPromise((resolve2, reject) => {
+        const promise = newPromise((resolve3, reject) => {
           stream._pendingAbortRequest = {
             _promise: void 0,
-            _resolve: resolve2,
+            _resolve: resolve3,
             _reject: reject,
             _reason: reason,
             _wasAlreadyErroring: wasAlreadyErroring
@@ -38044,9 +38062,9 @@ var require_ponyfill_es2018 = __commonJS({
         if (state === "closed" || state === "errored") {
           return promiseRejectedWith(new TypeError(`The stream (in ${state} state) is not in the writable state and cannot be closed`));
         }
-        const promise = newPromise((resolve2, reject) => {
+        const promise = newPromise((resolve3, reject) => {
           const closeRequest = {
-            _resolve: resolve2,
+            _resolve: resolve3,
             _reject: reject
           };
           stream._closeRequest = closeRequest;
@@ -38059,9 +38077,9 @@ var require_ponyfill_es2018 = __commonJS({
         return promise;
       }
       function WritableStreamAddWriteRequest(stream) {
-        const promise = newPromise((resolve2, reject) => {
+        const promise = newPromise((resolve3, reject) => {
           const writeRequest = {
-            _resolve: resolve2,
+            _resolve: resolve3,
             _reject: reject
           };
           stream._writeRequests.push(writeRequest);
@@ -38657,8 +38675,8 @@ var require_ponyfill_es2018 = __commonJS({
         return new TypeError("Cannot " + name + " a stream using a released writer");
       }
       function defaultWriterClosedPromiseInitialize(writer) {
-        writer._closedPromise = newPromise((resolve2, reject) => {
-          writer._closedPromise_resolve = resolve2;
+        writer._closedPromise = newPromise((resolve3, reject) => {
+          writer._closedPromise_resolve = resolve3;
           writer._closedPromise_reject = reject;
           writer._closedPromiseState = "pending";
         });
@@ -38694,8 +38712,8 @@ var require_ponyfill_es2018 = __commonJS({
         writer._closedPromiseState = "resolved";
       }
       function defaultWriterReadyPromiseInitialize(writer) {
-        writer._readyPromise = newPromise((resolve2, reject) => {
-          writer._readyPromise_resolve = resolve2;
+        writer._readyPromise = newPromise((resolve3, reject) => {
+          writer._readyPromise_resolve = resolve3;
           writer._readyPromise_reject = reject;
         });
         writer._readyPromiseState = "pending";
@@ -38764,7 +38782,7 @@ var require_ponyfill_es2018 = __commonJS({
         source._disturbed = true;
         let shuttingDown = false;
         let currentWrite = promiseResolvedWith(void 0);
-        return newPromise((resolve2, reject) => {
+        return newPromise((resolve3, reject) => {
           let abortAlgorithm;
           if (signal !== void 0) {
             abortAlgorithm = () => {
@@ -38905,7 +38923,7 @@ var require_ponyfill_es2018 = __commonJS({
             if (isError2) {
               reject(error2);
             } else {
-              resolve2(void 0);
+              resolve3(void 0);
             }
           }
         });
@@ -39169,8 +39187,8 @@ var require_ponyfill_es2018 = __commonJS({
         let branch1;
         let branch2;
         let resolveCancelPromise;
-        const cancelPromise = newPromise((resolve2) => {
-          resolveCancelPromise = resolve2;
+        const cancelPromise = newPromise((resolve3) => {
+          resolveCancelPromise = resolve3;
         });
         function pullAlgorithm() {
           if (reading) {
@@ -39260,8 +39278,8 @@ var require_ponyfill_es2018 = __commonJS({
         let branch1;
         let branch2;
         let resolveCancelPromise;
-        const cancelPromise = newPromise((resolve2) => {
-          resolveCancelPromise = resolve2;
+        const cancelPromise = newPromise((resolve3) => {
+          resolveCancelPromise = resolve3;
         });
         function forwardReaderError(thisReader) {
           uponRejection(thisReader._closedPromise, (r2) => {
@@ -39950,8 +39968,8 @@ var require_ponyfill_es2018 = __commonJS({
           const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);
           const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);
           let startPromise_resolve;
-          const startPromise = newPromise((resolve2) => {
-            startPromise_resolve = resolve2;
+          const startPromise = newPromise((resolve3) => {
+            startPromise_resolve = resolve3;
           });
           InitializeTransformStream(this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm);
           SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);
@@ -40042,8 +40060,8 @@ var require_ponyfill_es2018 = __commonJS({
         if (stream._backpressureChangePromise !== void 0) {
           stream._backpressureChangePromise_resolve();
         }
-        stream._backpressureChangePromise = newPromise((resolve2) => {
-          stream._backpressureChangePromise_resolve = resolve2;
+        stream._backpressureChangePromise = newPromise((resolve3) => {
+          stream._backpressureChangePromise_resolve = resolve3;
         });
         stream._backpressure = backpressure;
       }
@@ -42054,7 +42072,7 @@ var init_abort_error = __esm({
 
 // ../node_modules/.pnpm/node-fetch@3.3.2/node_modules/node-fetch/src/index.js
 async function fetch2(url, options_) {
-  return new Promise((resolve2, reject) => {
+  return new Promise((resolve3, reject) => {
     const request = new Request2(url, options_);
     const { parsedURL, options } = getNodeRequestOptions(request);
     if (!supportedSchemas.has(parsedURL.protocol)) {
@@ -42063,7 +42081,7 @@ async function fetch2(url, options_) {
     if (parsedURL.protocol === "data:") {
       const data = dist_default(request.url);
       const response2 = new Response2(data, { headers: { "Content-Type": data.typeFull } });
-      resolve2(response2);
+      resolve3(response2);
       return;
     }
     const send = (parsedURL.protocol === "https:" ? import_node_https.default : import_node_http2.default).request;
@@ -42185,7 +42203,7 @@ async function fetch2(url, options_) {
             if (responseReferrerPolicy) {
               requestOptions.referrerPolicy = responseReferrerPolicy;
             }
-            resolve2(fetch2(new Request2(locationURL, requestOptions)));
+            resolve3(fetch2(new Request2(locationURL, requestOptions)));
             finalize();
             return;
           }
@@ -42218,7 +42236,7 @@ async function fetch2(url, options_) {
       const codings = headers.get("Content-Encoding");
       if (!request.compress || request.method === "HEAD" || codings === null || response_.statusCode === 204 || response_.statusCode === 304) {
         response = new Response2(body, responseOptions);
-        resolve2(response);
+        resolve3(response);
         return;
       }
       const zlibOptions = {
@@ -42232,7 +42250,7 @@ async function fetch2(url, options_) {
           }
         });
         response = new Response2(body, responseOptions);
-        resolve2(response);
+        resolve3(response);
         return;
       }
       if (codings === "deflate" || codings === "x-deflate") {
@@ -42256,12 +42274,12 @@ async function fetch2(url, options_) {
             });
           }
           response = new Response2(body, responseOptions);
-          resolve2(response);
+          resolve3(response);
         });
         raw2.once("end", () => {
           if (!response) {
             response = new Response2(body, responseOptions);
-            resolve2(response);
+            resolve3(response);
           }
         });
         return;
@@ -42273,11 +42291,11 @@ async function fetch2(url, options_) {
           }
         });
         response = new Response2(body, responseOptions);
-        resolve2(response);
+        resolve3(response);
         return;
       }
       response = new Response2(body, responseOptions);
-      resolve2(response);
+      resolve3(response);
     });
     writeToStream(request_, request).catch(reject);
   });
@@ -42497,10 +42515,10 @@ var require_node_gyp_build = __commonJS({
         if (debug)
           return debug;
       }
-      var prebuild = resolve2(dir);
+      var prebuild = resolve3(dir);
       if (prebuild)
         return prebuild;
-      var nearby = resolve2(path4.dirname(process.execPath));
+      var nearby = resolve3(path4.dirname(process.execPath));
       if (nearby)
         return nearby;
       var target = [
@@ -42517,7 +42535,7 @@ var require_node_gyp_build = __commonJS({
         // eslint-disable-line
       ].filter(Boolean).join(" ");
       throw new Error("No native build was found for " + target + "\n    loaded from: " + dir + "\n");
-      function resolve2(dir2) {
+      function resolve3(dir2) {
         var tuples = readdirSync2(path4.join(dir2, "prebuilds")).map(parseTuple);
         var tuple = tuples.filter(matchTuple(platform, arch)).sort(compareTuples)[0];
         if (!tuple)
@@ -48190,13 +48208,13 @@ var require_dist_cjs13 = __commonJS({
         ...data.default && { default: data.default }
       }
     ), "getConfigData");
-    var import_path8 = require("path");
+    var import_path9 = require("path");
     var import_getHomeDir = require_getHomeDir();
     var ENV_CONFIG_PATH = "AWS_CONFIG_FILE";
-    var getConfigFilepath = /* @__PURE__ */ __name(() => process.env[ENV_CONFIG_PATH] || (0, import_path8.join)((0, import_getHomeDir.getHomeDir)(), ".aws", "config"), "getConfigFilepath");
+    var getConfigFilepath = /* @__PURE__ */ __name(() => process.env[ENV_CONFIG_PATH] || (0, import_path9.join)((0, import_getHomeDir.getHomeDir)(), ".aws", "config"), "getConfigFilepath");
     var import_getHomeDir2 = require_getHomeDir();
     var ENV_CREDENTIALS_PATH = "AWS_SHARED_CREDENTIALS_FILE";
-    var getCredentialsFilepath = /* @__PURE__ */ __name(() => process.env[ENV_CREDENTIALS_PATH] || (0, import_path8.join)((0, import_getHomeDir2.getHomeDir)(), ".aws", "credentials"), "getCredentialsFilepath");
+    var getCredentialsFilepath = /* @__PURE__ */ __name(() => process.env[ENV_CREDENTIALS_PATH] || (0, import_path9.join)((0, import_getHomeDir2.getHomeDir)(), ".aws", "credentials"), "getCredentialsFilepath");
     var prefixKeyRegex = /^([\w-]+)\s(["'])?([\w-@\+\.%:/]+)\2$/;
     var profileNameBlockList = ["__proto__", "profile __proto__"];
     var parseIni = /* @__PURE__ */ __name((iniData) => {
@@ -49350,7 +49368,7 @@ var require_dist_cjs20 = __commonJS({
         this.refillTokenBucket();
         if (amount > this.currentCapacity) {
           const delay = (amount - this.currentCapacity) / this.fillRate * 1e3;
-          await new Promise((resolve2) => setTimeout(resolve2, delay));
+          await new Promise((resolve3) => setTimeout(resolve3, delay));
         }
         this.currentCapacity = this.currentCapacity - amount;
       }
@@ -50318,18 +50336,18 @@ var require_dist_cjs28 = __commonJS({
       let hasError = false;
       if (expect === "100-continue") {
         await Promise.race([
-          new Promise((resolve2) => {
-            timeoutId = Number(setTimeout(resolve2, Math.max(MIN_WAIT_TIME, maxContinueTimeoutMs)));
+          new Promise((resolve3) => {
+            timeoutId = Number(setTimeout(resolve3, Math.max(MIN_WAIT_TIME, maxContinueTimeoutMs)));
           }),
-          new Promise((resolve2) => {
+          new Promise((resolve3) => {
             httpRequest.on("continue", () => {
               clearTimeout(timeoutId);
-              resolve2();
+              resolve3();
             });
             httpRequest.on("error", () => {
               hasError = true;
               clearTimeout(timeoutId);
-              resolve2();
+              resolve3();
             });
           })
         ]);
@@ -50365,13 +50383,13 @@ var require_dist_cjs28 = __commonJS({
       constructor(options) {
         this.socketWarningTimestamp = 0;
         this.metadata = { handlerProtocol: "http/1.1" };
-        this.configProvider = new Promise((resolve2, reject) => {
+        this.configProvider = new Promise((resolve3, reject) => {
           if (typeof options === "function") {
             options().then((_options) => {
-              resolve2(this.resolveDefaultConfig(_options));
+              resolve3(this.resolveDefaultConfig(_options));
             }).catch(reject);
           } else {
-            resolve2(this.resolveDefaultConfig(options));
+            resolve3(this.resolveDefaultConfig(options));
           }
         });
       }
@@ -50451,7 +50469,7 @@ var require_dist_cjs28 = __commonJS({
         let socketCheckTimeoutId;
         return new Promise((_resolve, _reject) => {
           let writeRequestBodyPromise = void 0;
-          const resolve2 = /* @__PURE__ */ __name(async (arg) => {
+          const resolve3 = /* @__PURE__ */ __name(async (arg) => {
             await writeRequestBodyPromise;
             clearTimeout(socketCheckTimeoutId);
             _resolve(arg);
@@ -50508,7 +50526,7 @@ var require_dist_cjs28 = __commonJS({
               headers: getTransformedHeaders(res.headers),
               body: res
             });
-            resolve2({ response: httpResponse });
+            resolve3({ response: httpResponse });
           });
           req.on("error", (err2) => {
             if (NODEJS_TIMEOUT_ERROR_CODES.includes(err2.code)) {
@@ -50685,13 +50703,13 @@ var require_dist_cjs28 = __commonJS({
       constructor(options) {
         this.metadata = { handlerProtocol: "h2" };
         this.connectionManager = new NodeHttp2ConnectionManager({});
-        this.configProvider = new Promise((resolve2, reject) => {
+        this.configProvider = new Promise((resolve3, reject) => {
           if (typeof options === "function") {
             options().then((opts) => {
-              resolve2(opts || {});
+              resolve3(opts || {});
             }).catch(reject);
           } else {
-            resolve2(options || {});
+            resolve3(options || {});
           }
         });
       }
@@ -50721,7 +50739,7 @@ var require_dist_cjs28 = __commonJS({
           var _a;
           let fulfilled = false;
           let writeRequestBodyPromise = void 0;
-          const resolve2 = /* @__PURE__ */ __name(async (arg) => {
+          const resolve3 = /* @__PURE__ */ __name(async (arg) => {
             await writeRequestBodyPromise;
             _resolve(arg);
           }, "resolve");
@@ -50777,7 +50795,7 @@ var require_dist_cjs28 = __commonJS({
               body: req
             });
             fulfilled = true;
-            resolve2({ response: httpResponse });
+            resolve3({ response: httpResponse });
             if (disableConcurrentStreams) {
               session.close();
               this.connectionManager.deleteSession(authority, session);
@@ -50860,7 +50878,7 @@ var require_dist_cjs28 = __commonJS({
       if (isReadableStreamInstance(stream)) {
         return collectReadableStream(stream);
       }
-      return new Promise((resolve2, reject) => {
+      return new Promise((resolve3, reject) => {
         const collector = new Collector();
         stream.pipe(collector);
         stream.on("error", (err2) => {
@@ -50870,7 +50888,7 @@ var require_dist_cjs28 = __commonJS({
         collector.on("error", reject);
         collector.on("finish", function() {
           const bytes = new Uint8Array(Buffer.concat(this.bufferedBytes));
-          resolve2(bytes);
+          resolve3(bytes);
         });
       });
     }, "streamCollector");
@@ -50931,7 +50949,7 @@ var require_dist_cjs29 = __commonJS({
     var import_protocol_http = require_dist_cjs2();
     var import_querystring_builder = require_dist_cjs27();
     function requestTimeout(timeoutInMs = 0) {
-      return new Promise((resolve2, reject) => {
+      return new Promise((resolve3, reject) => {
         if (timeoutInMs) {
           setTimeout(() => {
             const timeoutError = new Error(`Request did not complete within ${timeoutInMs} ms`);
@@ -51040,7 +51058,7 @@ var require_dist_cjs29 = __commonJS({
         ];
         if (abortSignal) {
           raceOfPromises.push(
-            new Promise((resolve2, reject) => {
+            new Promise((resolve3, reject) => {
               abortSignal.onabort = () => {
                 const abortError = new Error("Request aborted");
                 abortError.name = "AbortError";
@@ -51100,7 +51118,7 @@ var require_dist_cjs29 = __commonJS({
     }
     __name(collectStream, "collectStream");
     function readToBase64(blob) {
-      return new Promise((resolve2, reject) => {
+      return new Promise((resolve3, reject) => {
         const reader = new FileReader();
         reader.onloadend = () => {
           if (reader.readyState !== 2) {
@@ -51109,7 +51127,7 @@ var require_dist_cjs29 = __commonJS({
           const result = reader.result ?? "";
           const commaIndex = result.indexOf(",");
           const dataOffset = commaIndex > -1 ? commaIndex + 1 : result.length;
-          resolve2(result.substring(dataOffset));
+          resolve3(result.substring(dataOffset));
         };
         reader.onabort = () => reject(new Error("Read aborted"));
         reader.onerror = () => reject(reader.error);
@@ -52688,7 +52706,7 @@ var require_dist_cjs33 = __commonJS({
               const delayFromResponse = getDelayFromRetryAfterHeader(err2.$response);
               const delay = Math.max(delayFromResponse || 0, delayFromDecider);
               totalDelay += delay;
-              await new Promise((resolve2) => setTimeout(resolve2, delay));
+              await new Promise((resolve3) => setTimeout(resolve3, delay));
               continue;
             }
             if (!err2.$metadata) {
@@ -52856,7 +52874,7 @@ var require_dist_cjs33 = __commonJS({
             attempts = retryToken.getRetryCount();
             const delay = retryToken.getRetryDelay();
             totalRetryDelay += delay;
-            await new Promise((resolve2) => setTimeout(resolve2, delay));
+            await new Promise((resolve3) => setTimeout(resolve3, delay));
           }
         }
       } else {
@@ -53559,11 +53577,11 @@ function __metadata(metadataKey, metadataValue) {
 }
 function __awaiter(thisArg, _arguments, P, generator) {
   function adopt(value) {
-    return value instanceof P ? value : new P(function(resolve2) {
-      resolve2(value);
+    return value instanceof P ? value : new P(function(resolve3) {
+      resolve3(value);
     });
   }
-  return new (P || (P = Promise))(function(resolve2, reject) {
+  return new (P || (P = Promise))(function(resolve3, reject) {
     function fulfilled(value) {
       try {
         step(generator.next(value));
@@ -53579,7 +53597,7 @@ function __awaiter(thisArg, _arguments, P, generator) {
       }
     }
     function step(result) {
-      result.done ? resolve2(result.value) : adopt(result.value).then(fulfilled, rejected);
+      result.done ? resolve3(result.value) : adopt(result.value).then(fulfilled, rejected);
     }
     step((generator = generator.apply(thisArg, _arguments || [])).next());
   });
@@ -53793,14 +53811,14 @@ function __asyncValues(o) {
   }, i2);
   function verb(n) {
     i2[n] = o[n] && function(v) {
-      return new Promise(function(resolve2, reject) {
-        v = o[n](v), settle(resolve2, reject, v.done, v.value);
+      return new Promise(function(resolve3, reject) {
+        v = o[n](v), settle(resolve3, reject, v.done, v.value);
       });
     };
   }
-  function settle(resolve2, reject, d, v) {
+  function settle(resolve3, reject, d, v) {
     Promise.resolve(v).then(function(v2) {
-      resolve2({ value: v2, done: d });
+      resolve3({ value: v2, done: d });
     }, reject);
   }
 }
@@ -56918,11 +56936,11 @@ function __metadata2(metadataKey, metadataValue) {
 }
 function __awaiter2(thisArg, _arguments, P, generator) {
   function adopt(value) {
-    return value instanceof P ? value : new P(function(resolve2) {
-      resolve2(value);
+    return value instanceof P ? value : new P(function(resolve3) {
+      resolve3(value);
     });
   }
-  return new (P || (P = Promise))(function(resolve2, reject) {
+  return new (P || (P = Promise))(function(resolve3, reject) {
     function fulfilled(value) {
       try {
         step(generator.next(value));
@@ -56938,7 +56956,7 @@ function __awaiter2(thisArg, _arguments, P, generator) {
       }
     }
     function step(result) {
-      result.done ? resolve2(result.value) : adopt(result.value).then(fulfilled, rejected);
+      result.done ? resolve3(result.value) : adopt(result.value).then(fulfilled, rejected);
     }
     step((generator = generator.apply(thisArg, _arguments || [])).next());
   });
@@ -57144,14 +57162,14 @@ function __asyncValues2(o) {
   }, i2);
   function verb(n) {
     i2[n] = o[n] && function(v) {
-      return new Promise(function(resolve2, reject) {
-        v = o[n](v), settle(resolve2, reject, v.done, v.value);
+      return new Promise(function(resolve3, reject) {
+        v = o[n](v), settle(resolve3, reject, v.done, v.value);
       });
     };
   }
-  function settle(resolve2, reject, d, v) {
+  function settle(resolve3, reject, d, v) {
     Promise.resolve(v).then(function(v2) {
-      resolve2({ value: v2, done: d });
+      resolve3({ value: v2, done: d });
     }, reject);
   }
 }
@@ -57535,7 +57553,7 @@ var require_dist_cjs39 = __commonJS({
     var import_buffer2 = require("buffer");
     var import_http3 = require("http");
     function httpRequest(options) {
-      return new Promise((resolve2, reject) => {
+      return new Promise((resolve3, reject) => {
         var _a;
         const req = (0, import_http3.request)({
           method: "GET",
@@ -57565,7 +57583,7 @@ var require_dist_cjs39 = __commonJS({
             chunks.push(chunk);
           });
           res.on("end", () => {
-            resolve2(import_buffer2.Buffer.concat(chunks));
+            resolve3(import_buffer2.Buffer.concat(chunks));
             req.destroy();
           });
         });
@@ -57997,7 +58015,7 @@ var require_retry_wrapper = __commonJS({
           try {
             return await toRetry();
           } catch (e2) {
-            await new Promise((resolve2) => setTimeout(resolve2, delayMs));
+            await new Promise((resolve3) => setTimeout(resolve3, delayMs));
           }
         }
         return await toRetry();
@@ -59366,9 +59384,9 @@ var require_dist_cjs46 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/auth/httpAuthSchemeProvider.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/auth/httpAuthSchemeProvider.js
 var require_httpAuthSchemeProvider3 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/auth/httpAuthSchemeProvider.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/auth/httpAuthSchemeProvider.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.resolveHttpAuthSchemeConfig = exports2.defaultSSOOIDCHttpAuthSchemeProvider = exports2.defaultSSOOIDCHttpAuthSchemeParametersProvider = void 0;
@@ -59435,9 +59453,9 @@ var require_httpAuthSchemeProvider3 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/package.json
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/package.json
 var require_package4 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/package.json"(exports2, module2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/package.json"(exports2, module2) {
     module2.exports = {
       name: "@aws-sdk/client-sso-oidc",
       description: "AWS SDK for JavaScript Sso Oidc Client for Node.js, Browser and React Native",
@@ -59541,9 +59559,9 @@ var require_package4 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/ruleset.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/ruleset.js
 var require_ruleset2 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/ruleset.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/ruleset.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.ruleSet = void 0;
@@ -59576,9 +59594,9 @@ var require_ruleset2 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/endpointResolver.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/endpointResolver.js
 var require_endpointResolver2 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/endpointResolver.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/endpoint/endpointResolver.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.defaultEndpointResolver = void 0;
@@ -59596,9 +59614,9 @@ var require_endpointResolver2 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.shared.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.shared.js
 var require_runtimeConfig_shared2 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.shared.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.shared.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.getRuntimeConfig = void 0;
@@ -59642,9 +59660,9 @@ var require_runtimeConfig_shared2 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.js
 var require_runtimeConfig2 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/runtimeConfig.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.getRuntimeConfig = void 0;
@@ -59695,9 +59713,9 @@ var require_runtimeConfig2 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/index.js
+// ../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/index.js
 var require_dist_cjs47 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/index.js"(exports2, module2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sso-oidc@3.583.0_@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sso-oidc/dist-cjs/index.js"(exports2, module2) {
     "use strict";
     var __defProp3 = Object.defineProperty;
     var __getOwnPropDesc3 = Object.getOwnPropertyDescriptor;
@@ -61043,9 +61061,9 @@ Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.ht
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthSchemeProvider.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthSchemeProvider.js
 var require_httpAuthSchemeProvider4 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthSchemeProvider.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthSchemeProvider.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.resolveHttpAuthSchemeConfig = exports2.resolveStsAuthConfig = exports2.defaultSTSHttpAuthSchemeProvider = exports2.defaultSTSHttpAuthSchemeParametersProvider = void 0;
@@ -61115,9 +61133,9 @@ var require_httpAuthSchemeProvider4 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/EndpointParameters.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/EndpointParameters.js
 var require_EndpointParameters = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/EndpointParameters.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/EndpointParameters.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.commonParams = exports2.resolveClientEndpointParameters = void 0;
@@ -61141,9 +61159,9 @@ var require_EndpointParameters = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/package.json
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/package.json
 var require_package5 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/package.json"(exports2, module2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/package.json"(exports2, module2) {
     module2.exports = {
       name: "@aws-sdk/client-sts",
       description: "AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native",
@@ -61249,9 +61267,9 @@ var require_package5 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/ruleset.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/ruleset.js
 var require_ruleset3 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/ruleset.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/ruleset.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.ruleSet = void 0;
@@ -61296,9 +61314,9 @@ var require_ruleset3 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/endpointResolver.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/endpointResolver.js
 var require_endpointResolver3 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/endpointResolver.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/endpoint/endpointResolver.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.defaultEndpointResolver = void 0;
@@ -61316,9 +61334,9 @@ var require_endpointResolver3 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.shared.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.shared.js
 var require_runtimeConfig_shared3 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.shared.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.shared.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.getRuntimeConfig = void 0;
@@ -61362,9 +61380,9 @@ var require_runtimeConfig_shared3 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.js
 var require_runtimeConfig3 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeConfig.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.getRuntimeConfig = void 0;
@@ -61428,9 +61446,9 @@ var require_runtimeConfig3 = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthExtensionConfiguration.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthExtensionConfiguration.js
 var require_httpAuthExtensionConfiguration = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthExtensionConfiguration.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/auth/httpAuthExtensionConfiguration.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.resolveHttpAuthRuntimeConfig = exports2.getHttpAuthExtensionConfiguration = void 0;
@@ -61476,9 +61494,9 @@ var require_httpAuthExtensionConfiguration = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeExtensions.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeExtensions.js
 var require_runtimeExtensions = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeExtensions.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/runtimeExtensions.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.resolveRuntimeExtensions = void 0;
@@ -61507,9 +61525,9 @@ var require_runtimeExtensions = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/STSClient.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/STSClient.js
 var require_STSClient = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/STSClient.js"(exports2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/STSClient.js"(exports2) {
     "use strict";
     Object.defineProperty(exports2, "__esModule", { value: true });
     exports2.STSClient = exports2.__Client = void 0;
@@ -61571,9 +61589,9 @@ var require_STSClient = __commonJS({
   }
 });
 
-// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/index.js
+// ../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/index.js
 var require_dist_cjs50 = __commonJS({
-  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0_@aws-sdk+client-sso-oidc@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/index.js"(exports2, module2) {
+  "../node_modules/.pnpm/@aws-sdk+client-sts@3.583.0/node_modules/@aws-sdk/client-sts/dist-cjs/index.js"(exports2, module2) {
     "use strict";
     var __defProp3 = Object.defineProperty;
     var __getOwnPropDesc3 = Object.getOwnPropertyDescriptor;
@@ -72559,14 +72577,14 @@ var require_event = __commonJS({
         this._done = false;
         let futReject = null;
         let futResolve = null;
-        this._promise = new Promise((resolve2, reject) => {
+        this._promise = new Promise((resolve3, reject) => {
           futReject = (reason) => {
             this._done = true;
             reject(reason);
           };
           futResolve = (value) => {
             this._done = true;
-            resolve2(value);
+            resolve3(value);
           };
         });
         if (!futReject || !futResolve) {
@@ -72597,8 +72615,8 @@ var require_queues = __commonJS({
         this._promises = [];
       }
       _add() {
-        this._promises.push(new Promise((resolve2, reject) => {
-          this._resolvers.push(resolve2);
+        this._promises.push(new Promise((resolve3, reject) => {
+          this._resolvers.push(resolve3);
           this._rejecters.push(reject);
         }));
       }
@@ -72606,12 +72624,12 @@ var require_queues = __commonJS({
         if (!this._resolvers.length) {
           this._add();
         }
-        const resolve2 = this._resolvers.shift();
+        const resolve3 = this._resolvers.shift();
         this._rejecters.shift();
-        if (!resolve2) {
+        if (!resolve3) {
           throw new errors_1.InternalClientError("resolve function was null or undefined when attempting to push.");
         }
-        resolve2(item);
+        resolve3(item);
       }
       get() {
         if (!this._promises.length) {
@@ -73658,10 +73676,10 @@ var require_systemUtils = __commonJS({
         input: node_process_1.default.stdin,
         output
       });
-      return new Promise((resolve2) => {
+      return new Promise((resolve3) => {
         rl.question(message, (val2) => {
           rl.close();
-          resolve2(val2);
+          resolve3(val2);
         });
         silent = true;
       });
@@ -85667,7 +85685,7 @@ __export(introspect_exports, {
   introspectSqlite: () => introspectSqlite,
   relationsToTypeScript: () => relationsToTypeScript
 });
-var import_fs11, import_hanji14, import_path7, import_pluralize, introspectPostgres, introspectGel, introspectMysql, introspectSingleStore, introspectSqlite, introspectLibSQL, withCasing4, relationsToTypeScript;
+var import_fs11, import_hanji14, import_path8, import_pluralize, introspectPostgres, introspectGel, introspectMysql, introspectSingleStore, introspectSqlite, introspectLibSQL, withCasing4, relationsToTypeScript;
 var init_introspect = __esm({
   "src/cli/commands/introspect.ts"() {
     "use strict";
@@ -85675,7 +85693,7 @@ var init_introspect = __esm({
     import_fs11 = require("fs");
     import_hanji14 = __toESM(require_hanji());
     init_mjs();
-    import_path7 = require("path");
+    import_path8 = require("path");
     import_pluralize = __toESM(require_pluralize());
     init_singlestoreSchema();
     init_global();
@@ -85738,9 +85756,9 @@ var init_introspect = __esm({
       const ts = schemaToTypeScript4(schema6, casing2);
       const relationsTs = relationsToTypeScript(schema6, casing2);
       const { internal, ...schemaWithoutInternals } = schema6;
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
-      const relationsFile = (0, import_path7.join)(out, "relations.ts");
+      const relationsFile = (0, import_path8.join)(out, "relations.ts");
       (0, import_fs11.writeFileSync)(relationsFile, relationsTs.file);
       console.log();
       const { snapshots, journal } = prepareOutFolder(out, "postgresql");
@@ -85833,9 +85851,9 @@ var init_introspect = __esm({
       const ts = schemaToTypeScript2(schema6, casing2);
       const relationsTs = relationsToTypeScript(schema6, casing2);
       const { internal, ...schemaWithoutInternals } = schema6;
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
-      const relationsFile = (0, import_path7.join)(out, "relations.ts");
+      const relationsFile = (0, import_path8.join)(out, "relations.ts");
       (0, import_fs11.writeFileSync)(relationsFile, relationsTs.file);
       console.log();
       (0, import_hanji14.render)(
@@ -85888,9 +85906,9 @@ var init_introspect = __esm({
       const ts = schemaToTypeScript3(schema6, casing2);
       const relationsTs = relationsToTypeScript(schema6, casing2);
       const { internal, ...schemaWithoutInternals } = schema6;
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
-      const relationsFile = (0, import_path7.join)(out, "relations.ts");
+      const relationsFile = (0, import_path8.join)(out, "relations.ts");
       (0, import_fs11.writeFileSync)(relationsFile, relationsTs.file);
       console.log();
       const { snapshots, journal } = prepareOutFolder(out, "mysql");
@@ -85970,7 +85988,7 @@ var init_introspect = __esm({
       const schema6 = { id: originUUID, prevId: "", ...res };
       const ts = schemaToTypeScript5(schema6, casing2);
       const { internal, ...schemaWithoutInternals } = schema6;
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
       console.log();
       const { snapshots, journal } = prepareOutFolder(out, "postgresql");
@@ -86043,9 +86061,9 @@ var init_introspect = __esm({
       const schema6 = { id: originUUID, prevId: "", ...res };
       const ts = schemaToTypeScript(schema6, casing2);
       const relationsTs = relationsToTypeScript(schema6, casing2);
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
-      const relationsFile = (0, import_path7.join)(out, "relations.ts");
+      const relationsFile = (0, import_path8.join)(out, "relations.ts");
       (0, import_fs11.writeFileSync)(relationsFile, relationsTs.file);
       console.log();
       const { snapshots, journal } = prepareOutFolder(out, "sqlite");
@@ -86125,9 +86143,9 @@ var init_introspect = __esm({
       const schema6 = { id: originUUID, prevId: "", ...res };
       const ts = schemaToTypeScript(schema6, casing2);
       const relationsTs = relationsToTypeScript(schema6, casing2);
-      const schemaFile = (0, import_path7.join)(out, "schema.ts");
+      const schemaFile = (0, import_path8.join)(out, "schema.ts");
       (0, import_fs11.writeFileSync)(schemaFile, ts.file);
-      const relationsFile = (0, import_path7.join)(out, "relations.ts");
+      const relationsFile = (0, import_path8.join)(out, "relations.ts");
       (0, import_fs11.writeFileSync)(relationsFile, relationsTs.file);
       console.log();
       const { snapshots, journal } = prepareOutFolder(out, "sqlite");
@@ -90157,9 +90175,9 @@ var require_clone = __commonJS2({
           } else if (_instanceof(parent2, nativeSet)) {
             child = new nativeSet();
           } else if (_instanceof(parent2, nativePromise)) {
-            child = new nativePromise(function(resolve2, reject) {
+            child = new nativePromise(function(resolve3, reject) {
               parent2.then(function(value) {
-                resolve2(_clone(value, depth2 - 1));
+                resolve3(_clone(value, depth2 - 1));
               }, function(err2) {
                 reject(_clone(err2, depth2 - 1));
               });
@@ -91623,9 +91641,9 @@ var import_promises = require("fs/promises");
 var import_node_child_process = require("node:child_process");
 var import_path2 = require("path");
 function runCommand(command, options = {}) {
-  return new Promise((resolve2) => {
+  return new Promise((resolve3) => {
     (0, import_node_child_process.exec)(command, options, (error2) => {
-      return resolve2({ exitCode: (error2 == null ? void 0 : error2.code) ?? 0 });
+      return resolve3({ exitCode: (error2 == null ? void 0 : error2.code) ?? 0 });
     });
   });
 }
@@ -91694,14 +91712,14 @@ init_source();
 var import_fs7 = require("fs");
 var import_fs8 = __toESM(require("fs"));
 var import_hanji4 = __toESM(require_hanji());
-var import_path6 = require("path");
+var import_path7 = require("path");
 init_views();
 init_migrate();
 var dropMigration = async ({
   out,
   bundle
 }) => {
-  const metaFilePath = (0, import_path6.join)(out, "meta", "_journal.json");
+  const metaFilePath = (0, import_path7.join)(out, "meta", "_journal.json");
   const journal = JSON.parse((0, import_fs7.readFileSync)(metaFilePath, "utf-8"));
   if (journal.entries.length === 0) {
     console.log(
@@ -91717,8 +91735,8 @@ var dropMigration = async ({
     ...journal,
     entries: journal.entries.filter(Boolean)
   };
-  const sqlFilePath = (0, import_path6.join)(out, `${result.data.tag}.sql`);
-  const snapshotFilePath = (0, import_path6.join)(
+  const sqlFilePath = (0, import_path7.join)(out, `${result.data.tag}.sql`);
+  const snapshotFilePath = (0, import_path7.join)(
     out,
     "meta",
     `${result.data.tag.split("_")[0]}_snapshot.json`
@@ -91728,7 +91746,7 @@ var dropMigration = async ({
   (0, import_fs7.writeFileSync)(metaFilePath, JSON.stringify(resultJournal, null, 2));
   if (bundle) {
     import_fs8.default.writeFileSync(
-      (0, import_path6.join)(out, `migrations.js`),
+      (0, import_path7.join)(out, `migrations.js`),
       embeddedMigrations(resultJournal)
     );
   }
diff --git a/utils.js b/utils.js
index 7138fccccb595df131ddb9ad36deb33dc8717b63..26110c69c876779641eaa582e4fa722fb2f18361 100644
--- a/utils.js
+++ b/utils.js
@@ -6211,7 +6211,14 @@ var kloudMeta = () => {
     sqlite: []
   };
 };
+var mkdirSQLiteUrl = (it) => {
+  if (it.startsWith("file:")) {
+    it = it.substring(5);
+  }
+  (0, import_fs.mkdirSync)((0, import_path.dirname)(it), { recursive: true });
+};
 var normaliseSQLiteUrl = (it, type) => {
+  mkdirSQLiteUrl(it);
   if (type === "libsql") {
     if (it.startsWith("file:")) {
       return it;
diff --git a/utils.mjs b/utils.mjs
index 8551a871b73287ffb7640536eb803e416f73d8fc..0415dec8ab1c944507534c2681c3339e8e553d0e 100644
--- a/utils.mjs
+++ b/utils.mjs
@@ -1053,7 +1053,7 @@ var source_default = chalk;
 
 // src/utils.ts
 import { existsSync, mkdirSync, readdirSync, readFileSync, writeFileSync } from "fs";
-import { join } from "path";
+import { dirname, join } from "path";
 import { parse } from "url";
 
 // src/cli/views.ts
@@ -6188,7 +6188,14 @@ var kloudMeta = () => {
     sqlite: []
   };
 };
+var mkdirSQLiteUrl = (it) => {
+  if (it.startsWith("file:")) {
+    it = it.substring(5);
+  }
+  mkdirSync(dirname(it), { recursive: true });
+};
 var normaliseSQLiteUrl = (it, type) => {
+  mkdirSQLiteUrl(it);
   if (type === "libsql") {
     if (it.startsWith("file:")) {
       return it;
