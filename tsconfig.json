{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "lib": ["DOM", "DOM.Iterable", "ES2020"], "jsx": "react", "allowSyntheticDefaultImports": true, "sourceMap": true, "outDir": "./dist", "removeComments": true, "noEmit": true, "forceConsistentCasingInFileNames": true, "strict": true, "alwaysStrict": true, "skipLibCheck": true, "types": ["node", "deno"]}, "include": ["core/**/*", "cloud/**/*", "tests/**/*", "cli/**/*", "dashboard/**/*", "use-fireproof/**/*"], "exclude": ["**/dist/**"]}