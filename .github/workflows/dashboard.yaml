name: '@fireproof/dashboard'
on:
  push:
    paths:
      - 'dashboard/**/*'

    branches:
      - main
      - 'mabels/**'
      - 'jchris/**'

jobs:
  compile_test:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    steps:
      - uses: actions/checkout@v4

      - uses: ./dashboard/actions/base
        with:
          VITE_CLERK_PUBLISHABLE_KEY: ${{ vars.CLERK_PUBLISHABLE_KEY }}
