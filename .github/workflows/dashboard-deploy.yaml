name: '@fireproof/dashboard-deploy'
on:
  push:
    tags:
      - 'dashboard@*'

jobs:
  ci:
    environment: ${{ startsWith(github.ref, 'refs/tags/dashboard@s') && 'staging' || startsWith(github.ref, 'refs/tags/dashboard@p') && 'production' || 'dev' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./dashboard/actions/base
        with:
          CLERK_PUBLISHABLE_KEY: ${{ vars.CLERK_PUBLISHABLE_KEY }}

      - uses: ./dashboard/actions/deploy
        with:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ vars.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_D1_TOKEN: ${{ secrets.CLOUDFLARE_D1_TOKEN }}
          CLOUDFLARE_DATABASE_ID: ${{ vars.CLOUDFLARE_DATABASE_ID }}
          # need in CF to be Env
          CLOUD_SESSION_TOKEN_PUBLIC: ${{ vars.CLOUD_SESSION_TOKEN_PUBLIC }}
          CLOUD_SESSION_TOKEN_SECRET: ${{ secrets.CLOUD_SESSION_TOKEN_SECRET }}
          CLERK_PUBLISHABLE_KEY: ${{ vars.CLERK_PUBLISHABLE_KEY }}
          CLERK_PUB_JWT_URL: ${{ vars.CLERK_PUB_JWT_URL }}
          CLERK_PUB_JWT_KEY: ${{ vars.CLERK_PUB_JWT_KEY }}
          # need during build
          CLOUDFLARE_ENV: ${{vars.CLOUDFLARE_ENV }}
