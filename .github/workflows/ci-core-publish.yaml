name: '@fireproof/core-publish'
on:
  push:
    tags:
      - 'core@*'

jobs:
  compile_test:
    runs-on: ubuntu-latest
    environment: ${{ startsWith(github.ref, 'refs/tags/core@s') && 'staging' || startsWith(github.ref, 'refs/tags/core@p') && 'production' || 'dev' }}
    steps:
      - uses: actions/checkout@v4

      - uses: ./actions/base

      - uses: ./actions/core-publish
        with:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
