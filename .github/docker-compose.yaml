services:
  registry:
    image: "verdaccio/verdaccio"
    #volumes:
    #  - ./verdaccio.config.yaml:/verdaccio/conf/config.yaml
    ports:
      - "4873:4873"
    # docker run -p 4873:4873 -v $(pwd)/verdaccio.config.yaml:/verdaccio/conf/config.yaml -ti verdaccio/verdaccio
    volumes:
      - ${HOME}/.cache/vd:/verdaccio/storage/data
  esm-sh:
    # image: "ghcr.io/esm-dev/esm.sh:v136"
    image: "ghcr.io/esm-dev/esm.sh:latest"
    # image: "ghcr.io/esm-dev/esm.sh:v135_7"
    environment:
      NPM_REGISTRY: http://registry:4873/
    volumes:
      - ${HOME}/.cache/esm:/home/<USER>
    ports:
      - "4874:8080"
  wait-for-ready:
    image: curlimages/curl
    #command: "sleep 86400"
    entrypoint: ["/bin/sh", "-c", "--", "while true; do curl -f http://registry:4873; curl -f http://esm-sh:8080; sleep 10; done;"]
    depends_on:
      registry:
        condition: service_started
      esm-sh:
        condition: service_started
    healthcheck:
      test:
        - CMD-SHELL
        - "curl -f http://registry:4873 && curl -f http://esm-sh:8080"
      interval: 5s
      retries: 30
      timeout: 2s
