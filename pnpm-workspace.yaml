packages:
  - "core/*"
  - "cloud/*"
  - "cloud/backend/*"
  - "cli"
  - "core/types/*"
  - "core/types/protocols/*"
  - "core/gateways/*"
  - "core/protocols/*"
  - "use-fireproof/*"
  - "dashboard"
  - "vendor"
#catalog:
#  adviserCement: ^0.4.20
#
overrides:
#  "@types/node": "^24.0.10"
#  "@adviser/cement": "^0.4.20"
#  "wrangler": "^4.24.0"
#  "@cloudflare/workers-types": "^4.20250705.0"
#  "@clerk/backend": "^2.3.1"
#  "@libsql/client": "^0.15.9"
#  "@fireproof/vendor": "^3.0.0"
#  "ws": "^8.18.3"
#  "@clerk/clerk-react": "^5.32.3"
#  "@ipld/car": "^5.4.1"
#  "@ipld/dag-cbor": "^9.2.4"
#  "@ipld/dag-json": "^10.2.4"
#  "@tailwindcss/container-queries": "^0.1.1"
#  "@tanstack/react-query": "^5.81.5"
#  "@web3-storage/pail": "^0.6.2"
#  "cborg": "^4.2.12"
#  "charwise": "^3.0.1"
#  "dompurify": "^3.2.6"
#  "drizzle-orm": "^0.44.2"
#  "esbuild-plugin-replace": "^1.4.0"
#  "highlight.js": "^11.11.1"
#  "idb": "^8.0.3"
#  "jose": "^6.0.11"
#  "multiformats": "^13.3.7"
#  "p-map": "^7.0.3"
#  "p-retry": "^6.2.1"
#  "prolly-trees": "^1.0.4"
#  "randombytes": "^2.1.0"
#  "react": "^19.1.0"
#  "react-hook-form": "^7.59.0"
#  "react-router-dom": "^7.6.3"
#  "react-simple-code-editor": "^0.14.1"
#  "tailwindcss": "^3.4.17"
