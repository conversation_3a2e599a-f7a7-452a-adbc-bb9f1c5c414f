{"name": "@fireproof-example/react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "pnpm run '/build:/'", "build:tsc": "tsc", "build:vite": "vite build", "test": "vitest --run", "preview": "vite preview"}, "dependencies": {"use-fireproof": "0.0.0-smoke", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.24.0"}, "devDependencies": {"@testing-library/react": "^16.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "playwright": "^1.53.0", "playwright-chromium": "^1.53.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/browser": "^3.2.4", "@vitest/ui": "^3.2.4", "typescript": "^5.8.3", "vite": "^7.0.0", "vitest": "^3.2.4"}}