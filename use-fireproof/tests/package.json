{"name": "use-fireproof-test", "version": "0.0.0", "description": "Fireproof live ledger, JavaScript API and React hooks", "type": "module", "homepage": "https://use-fireproof.com", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "scripts": {"build": "core-cli tsc", "test": "vitest --run", "pack": "core-cli build --doPack", "publish": "core-cli build"}, "author": "<PERSON>", "license": "AFL-2.0", "dependencies": {"@fireproof/core": "workspace:0.0.0", "@fireproof/core-gateways-cloud": "workspace:0.0.0", "@fireproof/core-types-blockstore": "workspace:0.0.0", "use-fireproof": "workspace:0.0.0"}, "keywords": ["react", "ledger", "json", "live", "sync"], "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@testing-library/react": "^16.3.0", "@types/deno": "^2.3.0", "@types/react": "^19.1.8", "@vitest/browser": "^3.2.4", "playwright": "^1.54.1", "playwright-chromium": "^1.54.1", "vitest": "^3.2.4"}}