{"name": "@fireproof/core-cli", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "module": "./index.js", "main": "./index.js", "bin": "./run.sh", "scripts": {"build": "core-cli tsc", "pack": "echo skip", "publish": "echo skip"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/cloud-backend-cf-d1": "workspace:0.0.0", "@fireproof/cloud-base": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "cmd-ts": "^0.13.0", "find-up": "^7.0.0", "fs-extra": "^11.3.0", "multiformats": "^13.3.7", "semver": "^7.7.2", "zx": "^8.7.1"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@types/fs-extra": "^11.0.4", "@types/semver": "^7.7.0"}}