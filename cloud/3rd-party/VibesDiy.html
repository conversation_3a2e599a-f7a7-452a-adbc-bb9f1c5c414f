<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:title" content="Electronic Music Sequencer" />
    <meta property="og:description" content="Electronic Music Sequencer - remix on Vibes DIY" />

    <meta property="og:url" content="https://silent-kestrel-5461.vibesdiy.app" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Electronic Music Sequencer" />
    <meta name="twitter:description" content="Electronic Music Sequencer - remix on Vibes DIY" />

    <meta name="twitter:url" content="https://silent-kestrel-5461.vibesdiy.app" />

    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" sizes="any" />
    <title>Electronic Music Sequencer - made on Vibes DIY</title>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://esm.sh/use-vibes@latest/dist/components/ImgGen.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      /* Hide indicator by default, show only during network activity */
      .indicator-svg {
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      body.network-active .indicator-svg {
        opacity: 1;
      }

      /* QR code absolute positioning */
      .qr-code {
        position: fixed; /* Fixed positioning relative to viewport */
        top: 10px;
        right: 10px;
        z-index: 1001; /* Higher than position-element z-index */
        background: rgba(255, 255, 255, 0.25);
        border-radius: 14px;
        border: 1px solid rgba(35, 136, 179, 0.1);
        padding: 10px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px); /* Safari support */
        display: none; /* Hidden by default */
      }

      /* Show QR code when expanded class is added to body */
      body.expanded .qr-code {
        display: block;
      }

      /* Cyber glitch effect for Remixable text */
      @keyframes glitch-anim-1 {
        0%,
        100% {
          clip-path: inset(50% 0 30% 0);
          transform: translate(-1px, 1px);
        }
        10% {
          clip-path: inset(10% 0 60% 0);
          transform: translate(1px, -1px);
        }
        20% {
          clip-path: inset(30% 0 20% 0);
          transform: translate(1px, 0);
        }
        30% {
          clip-path: inset(20% 0 30% 0);
          transform: translate(-1px, 0);
        }
        40% {
          clip-path: inset(60% 0 10% 0);
          transform: translate(1px, 1px);
        }
        50% {
          clip-path: inset(10% 0 40% 0);
          transform: translate(-1px, -1px);
        }
        60% {
          clip-path: inset(40% 0 20% 0);
          transform: translate(1px, -1px);
        }
        70% {
          clip-path: inset(50% 0 30% 0);
          transform: translate(-1px, 1px);
        }
        80% {
          clip-path: inset(20% 0 50% 0);
          transform: translate(0, 0);
        }
        90% {
          clip-path: inset(40% 0 30% 0);
          transform: translate(-1px, 1px);
        }
      }

      @keyframes glitch-anim-2 {
        0%,
        100% {
          clip-path: inset(20% 0 40% 0);
          transform: translate(1px, 0);
        }
        10% {
          clip-path: inset(40% 0 20% 0);
          transform: translate(-1px, 1px);
        }
        20% {
          clip-path: inset(60% 0 30% 0);
          transform: translate(0, 1px);
        }
        30% {
          clip-path: inset(10% 0 60% 0);
          transform: translate(0, -1px);
        }
        40% {
          clip-path: inset(30% 0 10% 0);
          transform: translate(1px, 0);
        }
        50% {
          clip-path: inset(50% 0 40% 0);
          transform: translate(-1px, 0);
        }
        60% {
          clip-path: inset(20% 0 30% 0);
          transform: translate(1px, -1px);
        }
        70% {
          clip-path: inset(40% 0 50% 0);
          transform: translate(-1px, 1px);
        }
        80% {
          clip-path: inset(10% 0 20% 0);
          transform: translate(0, 0);
        }
        90% {
          clip-path: inset(30% 0 10% 0);
          transform: translate(1px, 1px);
        }
      }

      .remixable-text {
        position: relative;
        display: inline-block;
        margin: 0;
        color: #333;
        font-weight: bold; /* Pre-set to bold to prevent size change */
        transition: all 0.1s ease;
        z-index: 2; /* Place original text on top */
      }

      .position-element:hover .remixable-text {
        /* No animation or color change on the text itself */
        color: #333; /* Keep the original color */
      }

      @keyframes text-blur {
        0% {
          text-shadow:
            -2px 0 #ff00cc,
            2px 0 #00ccff;
        }
        25% {
          text-shadow:
            -2px 0 #00ccff,
            2px 0 #ffcc00;
        }
        50% {
          text-shadow:
            2px 0 #00ff00,
            -2px 0 #ff00cc;
        }
        75% {
          text-shadow:
            1px 0 #ffcc00,
            -1px 0 #00ccff;
        }
        100% {
          text-shadow:
            -1px 0 #ff00cc,
            1px 0 #00ff00;
        }
      }

      .position-element:hover .remixable-text:before,
      .position-element:hover .remixable-text:after {
        content: "Remixable";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
        z-index: -1; /* Place behind the original text */
      }

      .position-element:hover .remixable-text:before {
        color: #ff00cc;
        animation: glitch-anim-1 8s infinite linear alternate-reverse;
      }

      .position-element:hover .remixable-text:after {
        color: #00ccff;
        animation: glitch-anim-2 60s infinite linear alternate-reverse;
      }

      /* Vibes DIY SVG logo glitch effect */
      .vibes-logo-container {
        position: relative;
        display: inline-block;
      }

      .vibes-logo-container::before,
      .vibes-logo-container::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://vibes.diy/vibes-diy.svg");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        z-index: -1;
        opacity: 0;
        transition:
          opacity 1.2s ease,
          transform 0.1s ease-out;
      }

      .vibes-logo-container {
        --moveX1: 0px;
        --moveY1: 0px;
        --moveX2: 0px;
        --moveY2: 0px;
      }

      .position-element:hover .vibes-logo-container::before {
        transform: translate(var(--moveX1), var(--moveY1));
      }

      .position-element:hover .vibes-logo-container::after {
        transform: translate(var(--moveX2), var(--moveY2));
      }

      /* Main logo transitions to color on hover */
      .vibes-logo-main {
        transition: filter 0.3s ease;
        filter: grayscale(1);
      }

      /* .position-element:hover .vibes-logo-main {
        filter: grayscale(0.2) contrast(1);
      } */

      .position-element:hover .vibes-logo-container::before,
      .position-element:hover .vibes-logo-container::after {
        opacity: 0.2;
      }

      .position-element:hover .vibes-logo-container::before {
        filter: hue-rotate(120deg) brightness(1.2) contrast(1.2) saturate(2);
        mix-blend-mode: exclusion;
      }

      .position-element:hover .vibes-logo-container::after {
        filter: hue-rotate(210deg) brightness(1.5) contrast(1.3) saturate(2.5);
        mix-blend-mode: screen;
      }

      /* Disable glitch effect when expanded */
      .position-element.expanded:hover .vibes-logo-container::before,
      .position-element.expanded:hover .vibes-logo-container::after {
        opacity: 0;
      }
      #container {
        width: 100%;
        height: 100vh;
      }

      /* Styles for expanded button state */
      .position-element.expanded .rounded-button {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        padding: 6px 10px;
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        height: 60px;
      }

      .position-element.expanded .content {
        max-width: none;
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin: 0;
        flex: 1;
      }

      .position-element.expanded .vibes-logo-container {
        margin-top: 4px;
        transform: translateY(10px);
        flex-shrink: 0;
      }

      /* Make logo larger when expanded */
      .position-element.expanded .vibes-logo-main {
        height: 52px !important;
        margin-top: -8px !important;
        filter: grayscale(0);
      }

      /* Hide/show expanded-only content */
      .expanded-only-content {
        display: none;
      }

      .position-element.expanded .expanded-only-content {
        display: flex;
        flex: 1;
        margin-left: 20px;
        width: 100%;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
      }

      /* Responsive layout for smaller screens */
      @media (max-width: 768px) {
        .position-element.expanded .expanded-only-content {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;
          margin-left: 0;
          padding: 0 12px;
        }

        .position-element.expanded .content {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;
        }

        /* Move logo to the left on mobile */
        .position-element.expanded .vibes-logo-container {
          align-self: flex-start;
          margin-left: 0;
        }

        .remix-form {
          width: 100%;
          flex-direction: column;
          gap: 8px;
        }

        /* Spread buttons apart */
        .button-group {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        }

        /* Make input wider with less padding */
        .remix-input {
          padding: 6px 12px;
          align-self: center;
          width: 100%;
          max-width: none;
        }

        .expanded-separator {
          display: none;
        }

        .position-element.expanded {
          width: 90%;
          right: 5%;
        }

        .position-element.expanded .rounded-button {
          padding: 12px 16px;
          height: auto;
          min-height: 60px;
        }
      }

      /* Even smaller screens */
      @media (max-width: 480px) {
        .position-element.expanded {
          width: 95%;
          right: 2.5%;
        }

        .remix-input {
          font-size: 16px; /* Prevents zoom on iOS */
        }
      }

      /* Remix form styles */
      .remix-form {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
      }

      .remix-input {
        flex: 1;
        min-width: 0;
        padding: 8px 14px;
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 8px;
        font-family: inherit;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.6);
        transition: all 0.2s ease;
        backdrop-filter: blur(5px);
      }

      .remix-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .remix-submit {
        background: #ccc;
        border: 1px solid rgba(59, 130, 246, 0.2);
        cursor: pointer;
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .remix-submit:hover {
        transform: scale(1.1);
      }

      .remix-link {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 22px;
        padding: 0 8px;
      }
      .remix-link:hover {
        transform: scale(1.1);
      }

      /* Add visual separators */
      .expanded-separator {
        border-left: 1px solid rgba(0, 0, 0, 0.15);
        height: 20px;
        margin: 0 8px;
      }

      /* Subtle pulse animation when collapsed */
      @keyframes gentle-pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }

      .position-element:not(.expanded) .rounded-button {
        animation: gentle-pulse 4s ease-in-out infinite;
      }

      /* Smooth fade-in for expanded content */
      .position-element.expanded .expanded-only-content {
        animation: fadeInFromRight 0.3s ease-out;
      }

      @keyframes fadeInFromRight {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .position-element {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        transition: all 0.3s ease-in-out;
      }

      .position-element.expanded {
        width: 75%;
        right: 12.5%;
      }

      .position-element:hover .rounded-button {
        background: rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        transform: translateY(-1px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;
      }
      .indicator-svg {
        position: absolute;
        top: -25px;
        left: -25px;
        z-index: 1;
        pointer-events: none; /* Allows clicking through to the SVG */
      }
      .indicator-svg img {
        width: 75px;
        height: auto;
        transition: all 0.3s ease;
        /* opacity: 0.85; */
      }

      /* Make indicator larger when expanded */
      .position-element.expanded .indicator-svg img {
        width: 100px;
      }

      .position-element.expanded .indicator-svg {
        top: -25px;
        left: -25px;
      }

      /* Adjust indicator position for smaller collapsed state */
      .indicator-svg {
        top: -19px;
        left: -19px;
      }

      /* Close button styles */
      .close-button {
        position: absolute;
        bottom: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        background: rgba(100, 100, 100, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 50%;
        display: none;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        z-index: 10;
        color: white;
      }

      .close-button:hover {
        background: rgba(80, 80, 80, 1);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
      }

      /* Show close button only when expanded */
      .position-element.expanded .close-button {
        display: flex;
      }
      .rounded-button {
        position: relative;
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.1);
        border-radius: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.6s ease;
        height: 38px;
        padding: 4px 10px;
        font-size: 14px;
        color: #333;
        text-decoration: none;
      }

      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: auto;
        margin-right: -15px;
        margin-top: 8px;
        max-width: 120px;
        transition: all 0.3s ease;
      }
    </style>
    <script>
      // Long press handler for Remix button
      document.addEventListener("DOMContentLoaded", () => {
        // Long press handler for Remix button
        const remixButton = document.querySelector(".position-element");
        if (!remixButton) return;

        let pressTimer;
        const longPressDuration = 500; // 500ms for long press

        // For touch devices
        remixButton.addEventListener("touchstart", (e) => {
          pressTimer = setTimeout(() => {
            remixButton.style.display = "none";
          }, longPressDuration);
        });

        remixButton.addEventListener("touchend", () => {
          clearTimeout(pressTimer);
        });

        remixButton.addEventListener("touchmove", () => {
          clearTimeout(pressTimer);
        });

        // For mouse devices
        remixButton.addEventListener("mousedown", (e) => {
          pressTimer = setTimeout(() => {
            remixButton.style.display = "none";
          }, longPressDuration);
        });

        remixButton.addEventListener("mouseup", () => {
          clearTimeout(pressTimer);
        });

        remixButton.addEventListener("mouseleave", () => {
          clearTimeout(pressTimer);
        });

        // Parallax effect for logo glitch based on mouse position
        const logoContainer = document.querySelector(".vibes-logo-container");
        if (logoContainer) {
          const roundedButton = document.querySelector(".rounded-button");

          roundedButton.addEventListener("mousemove", (e) => {
            // Get mouse position relative to the button
            const rect = roundedButton.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width - 0.5; // -0.5 to 0.5
            const y = (e.clientY - rect.top) / rect.height - 0.5; // -0.5 to 0.5

            // Calculate transform values (adjust multiplier for effect intensity)
            const intensity = 4;
            const moveX1 = x * intensity;
            const moveY1 = y * intensity;
            const moveX2 = x * intensity * -1.5;
            const moveY2 = y * intensity * -1.5;

            // Apply transforms using CSS custom properties
            logoContainer.style.setProperty("--moveX1", `${moveX1}px`);
            logoContainer.style.setProperty("--moveY1", `${moveY1}px`);
            logoContainer.style.setProperty("--moveX2", `${moveX2}px`);
            logoContainer.style.setProperty("--moveY2", `${moveY2}px`);
          });

          roundedButton.addEventListener("mouseleave", () => {
            // Reset positions when mouse leaves
            logoContainer.style.setProperty("--moveX1", "0px");
            logoContainer.style.setProperty("--moveY1", "0px");
            logoContainer.style.setProperty("--moveX2", "0px");
            logoContainer.style.setProperty("--moveY2", "0px");
          });
        }

        // Handle button click to resize and reposition element
        const positionElement = document.querySelector(".position-element");
        const contentLink = document.querySelector(".position-element span.content");

        if (positionElement && contentLink) {
          // Expanded state flag
          let isExpanded = false;

          // Store original styles for restoring later
          const originalStyles = {
            width: "",
            height: "",
            right: "",
            bottom: "",
            borderRadius: "",
            transform: "",
          };

          // Handle close button clicks
          const closeButton = document.querySelector(".close-button");
          if (closeButton) {
            closeButton.addEventListener("click", (e) => {
              e.stopPropagation();
              if (isExpanded) {
                // Restore original styles
                positionElement.style.width = originalStyles.width;
                positionElement.style.height = originalStyles.height;
                positionElement.style.right = originalStyles.right;
                positionElement.style.bottom = originalStyles.bottom;
                positionElement.style.borderRadius = originalStyles.borderRadius;
                positionElement.style.transform = originalStyles.transform;

                // Remove expanded class
                positionElement.classList.remove("expanded");
                // Remove expanded class from body
                document.body.classList.remove("expanded");

                isExpanded = false;
              }
            });
          }

          // Make entire position element clickable in collapsed state
          positionElement.addEventListener("click", (e) => {
            // Don't handle clicks on form elements, remix-link, or close button
            if (e.target.closest("#remix-form") || e.target.closest(".remix-link") || e.target.closest(".close-button")) {
              return;
            }

            // Handle logo clicks when expanded - navigate to vibes.diy
            if (isExpanded && e.target.closest(".vibes-logo-container")) {
              window.top.location.href = "https://vibes.diy";
              return;
            }

            // Prevent default on link clicks
            if (e.target.tagName === "A" || e.target.closest("a")) {
              e.preventDefault();
            }

            if (!isExpanded) {
              // Save original styles before changing
              originalStyles.width = positionElement.style.width;
              originalStyles.height = positionElement.style.height;
              originalStyles.right = positionElement.style.right;
              originalStyles.bottom = positionElement.style.bottom;
              originalStyles.borderRadius = positionElement.style.borderRadius;
              originalStyles.transform = positionElement.style.transform;

              // Expand and center at bottom
              positionElement.style.width = "75%";
              // Keep original height in expanded state
              positionElement.style.position = "fixed";
              positionElement.style.right = "12.5%";
              positionElement.style.bottom = "20px";
              positionElement.style.borderRadius = "16px";
              positionElement.style.transform = "none";
              positionElement.style.transition = "all 0.3s ease-in-out";
              positionElement.style.zIndex = "9999";

              // Add a class for any additional styling
              positionElement.classList.add("expanded");
              // Add expanded class to body for QR code visibility
              document.body.classList.add("expanded");

              isExpanded = true;
            } else {
              // Restore original styles
              positionElement.style.width = originalStyles.width;
              positionElement.style.height = originalStyles.height;
              positionElement.style.right = originalStyles.right;
              positionElement.style.bottom = originalStyles.bottom;
              positionElement.style.borderRadius = originalStyles.borderRadius;
              positionElement.style.transform = originalStyles.transform;

              // Remove expanded class
              positionElement.classList.remove("expanded");
              // Remove expanded class from body
              document.body.classList.remove("expanded");

              isExpanded = false;
            }
          });

          // Handle form submission
          const remixForm = document.getElementById("remix-form");
          if (remixForm) {
            remixForm.addEventListener("submit", (e) => {
              return;
              e.preventDefault();

              const input = remixForm.querySelector(".remix-input");
              const value = input.value.trim();

              if (value) {
                // Change background color based on input
                try {
                  // Set the body background color to the input value
                  document.body.style.backgroundColor = value;

                  // Show a subtle success indicator
                  input.style.backgroundColor = "rgba(220, 255, 220, 0.8)";
                  setTimeout(() => {
                    input.style.backgroundColor = "rgba(255, 255, 255, 0.5)";
                  }, 1000);

                  // Clear input
                  input.value = "";
                } catch (err) {
                  // Show error
                  input.style.backgroundColor = "rgba(255, 220, 220, 0.8)";
                  setTimeout(() => {
                    input.style.backgroundColor = "rgba(255, 255, 255, 0.5)";
                  }, 1000);
                }
              }
            });
          }
        }
      });
    </script>

    <!-- Network activity tracker script -->
    <script>
      // Network activity tracker for a single page
      (function () {
        const activeRequests = new Set();
        let lastState = null;

        // Function to update the DOM based on network state
        function updateNetworkState() {
          const currentState = activeRequests.size > 0;
          if (currentState !== lastState) {
            lastState = currentState;

            // Add or remove class on body based on network state
            if (currentState) {
              document.body.classList.add("network-active");

              // Restart SVG animations by targeting animation elements
              const indicatorImg = document.querySelector(".indicator-svg img");
              if (indicatorImg) {
                restartSvgAnimations(indicatorImg);
              }
            } else {
              document.body.classList.remove("network-active");

              // Clean up SVG animations when network activity ends
              const indicatorImg = document.querySelector(".indicator-svg img");
              if (indicatorImg) {
                cleanupSvgAnimations(indicatorImg);
              }
            }
          }
        }

        // Function to restart SVG animations
        function restartSvgAnimations(imgElement) {
          try {
            // Get the SVG document if it's an SVG
            const svgDoc = imgElement.contentDocument || imgElement.getSVGDocument?.();
            if (svgDoc) {
              // Find all animation elements and restart them
              const animations = svgDoc.querySelectorAll("animate, animateTransform, animateMotion, set");
              animations.forEach((anim) => {
                try {
                  anim.beginElement();
                } catch (e) {
                  // Fallback: manipulate begin attribute
                  const currentTime = svgDoc.documentElement.getCurrentTime?.() || 0;
                  anim.setAttribute("begin", currentTime + "s");
                }
              });
            } else {
              // For regular img elements, try to trigger re-parse by modifying src with timestamp
              const currentSrc = imgElement.src;
              if (currentSrc.includes("?")) {
                imgElement.src = currentSrc.split("?")[0] + "?t=" + Date.now();
              } else {
                imgElement.src = currentSrc + "?t=" + Date.now();
              }
            }
          } catch (e) {
            console.log("Could not restart SVG animation:", e);
          }
        }

        // Function to cleanup/stop SVG animations
        function cleanupSvgAnimations(imgElement) {
          try {
            // Get the SVG document if it's an SVG
            const svgDoc = imgElement.contentDocument || imgElement.getSVGDocument?.();
            if (svgDoc) {
              // Find all animation elements and end them
              const animations = svgDoc.querySelectorAll("animate, animateTransform, animateMotion, set");
              animations.forEach((anim) => {
                try {
                  anim.endElement();
                } catch (e) {
                  // Fallback: set begin to a future time to stop current animation
                  anim.setAttribute("begin", "indefinite");
                }
              });
            }
          } catch (e) {
            console.log("Could not cleanup SVG animation:", e);
          }
        }

        // Save original fetch function
        const originalFetch = window.fetch;

        // Override fetch to track requests
        window.fetch = (...args) => {
          const reqInfo = args[0];
          activeRequests.add(reqInfo);
          updateNetworkState();

          return originalFetch(...args)
            .then((res) => {
              if (!res.body) {
                activeRequests.delete(reqInfo);
                updateNetworkState();
                return res;
              }

              // Handle streaming responses
              const reader = res.body.getReader();
              const stream = new ReadableStream({
                start(controller) {
                  function pump() {
                    reader.read().then(({ done, value }) => {
                      if (done) {
                        activeRequests.delete(reqInfo);
                        updateNetworkState();
                        controller.close();
                        return;
                      }
                      controller.enqueue(value);
                      pump();
                    });
                  }
                  pump();
                },
              });
              return new Response(stream, { headers: res.headers });
            })
            .catch((err) => {
              // Make sure to clean up if fetch fails
              activeRequests.delete(reqInfo);
              updateNetworkState();
              throw err; // Re-throw the error
            });
        };
      })();
    </script>
  </head>
  <body>
    <div id="container"></div>

    <div class="qr-code">
      <a href="https://vibes.diy/vibe/silent-kestrel-5461" target="_top">
        <img
          src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://vibes.diy/vibe/silent-kestrel-5461"
          alt="QR Code"
        />
      </a>
    </div>

    <div class="position-element">
      <div class="rounded-button">
        <div class="indicator-svg">
          <img src="https://vibes.diy/indicate.svg" alt="Indicate" />
        </div>
        <div class="close-button">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          </svg>
        </div>
        <span class="content">
          <div class="vibes-logo-container">
            <img
              src="https://vibes.diy/vibes-diy.svg"
              alt="Vibes DIY"
              class="vibes-logo-main"
              style="
                height: 32px;
                display: block;
                margin: 0 auto;
                margin-top: -4px;
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
              "
            />
          </div>
          <div class="expanded-only-content flex items-center gap-2">
            <form
              id="remix-form"
              class="remix-form flex flex-grow items-center gap-2"
              action="https://vibes.diy/remix/silent-kestrel-5461"
              target="_top"
            >
              <input type="text" name="prompt" placeholder="Make it pink" class="remix-input flex-1 min-w-0" />
              <div class="button-group flex items-center gap-2">
                <button type="submit" class="text-xs font-mono pt-1 px-1 remix-submit flex-shrink-0">
                  <span class="remixable-text">Remix</span>
                </button>
              </div>
            </form>
          </div>
        </span>
      </div>
    </div>
    <script>
      window.CALLAI_API_KEY = "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";
    </script>
    <script type="importmap">
      {
        "imports": {
          "react": "https://esm.sh/react@19.1.0/es2022/react.mjs",
          "react-dom": "https://esm.sh/react-dom@19.1.0/es2022/react-dom.mjs",
          "react-dom/client": "https://esm.sh/react-dom@19.1.0/es2022/client.mjs",
          "use-fireproof": "https://esm.sh/use-fireproof@0.22.0-dev-preview",
          "call-ai": "https://esm.sh/call-ai",
          "use-vibes": "https://esm.sh/use-vibes",
          "three": "https://esm.sh/three"
        }
      }
    </script>
    <script type="text/babel" data-type="module">
      // "use-fireproof": "https://esm.sh/use-fireproof@0.20.5",
      import ReactDOMClient from "react-dom/client";

      // APP_CODE placeholder will be replaced with actual code
      // prettier-ignore
      import React, { useState, useEffect, useRef, useCallback } from "react"
      import { useFireproof } from "use-fireproof";
      import { callAI } from "call-ai";

      export default function App() {
        const { database, useLiveQuery, useDocument } = useFireproof("circuit-groove");
        const audioContextRef = useRef(null);
        const gainNodeRef = useRef(null);
        const intervalRef = useRef(null);
        const [isPlaying, setIsPlaying] = useState(false);
        const [currentStep, setCurrentStep] = useState(0);
        const [bpm, setBpm] = useState(120);

        // Current composition
        const {
          doc: composition,
          merge: mergeComposition,
          save: saveComposition,
        } = useDocument({
          name: "",
          bpm: 120,
          instruments: {
            kick: {
              pattern: [1, 0, 1, 0, 1, 0, 1, 0],
              frequency: 60,
              type: "sine",
              volume: 0.8,
              color: "#ff70a6",
            },
            snare: {
              pattern: [0, 1, 0, 1, 0, 1, 0, 1],
              frequency: 200,
              type: "square",
              volume: 0.6,
              color: "#70d6ff",
            },
            hihat: {
              pattern: [1, 1, 1, 1, 1, 1, 1, 1],
              frequency: 8000,
              type: "sawtooth",
              volume: 0.3,
              color: "#ffd670",
            },
            bass: {
              pattern: [1, 0, 0, 1, 0, 0, 1, 0],
              frequency: 80,
              type: "triangle",
              volume: 0.5,
              color: "#e9ff70",
            },
            lead: {
              pattern: [0, 0, 1, 0, 0, 1, 0, 0],
              frequency: 440,
              type: "sine",
              volume: 0.4,
              color: "#ff9770",
            },
          },
        });

        // Load saved compositions
        const { docs: savedCompositions } = useLiveQuery("type", {
          key: "composition",
          descending: true,
        });

        // Initialize Web Audio
        useEffect(() => {
          if (!audioContextRef.current) {
            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
            gainNodeRef.current = audioContextRef.current.createGain();
            gainNodeRef.current.connect(audioContextRef.current.destination);
            gainNodeRef.current.gain.value = 0.3;
          }
        }, []);

        // Play a single tone
        const playTone = useCallback((frequency, type, duration = 0.1, volume = 0.5) => {
          if (!audioContextRef.current) return;

          const oscillator = audioContextRef.current.createOscillator();
          const gainNode = audioContextRef.current.createGain();

          oscillator.type = type;
          oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);

          gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime);
          gainNode.gain.linearRampToValueAtTime(volume, audioContextRef.current.currentTime + 0.01);
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + duration);

          oscillator.connect(gainNode);
          gainNode.connect(gainNodeRef.current);

          oscillator.start(audioContextRef.current.currentTime);
          oscillator.stop(audioContextRef.current.currentTime + duration);
        }, []);

        // Play current step
        const playStep = useCallback(() => {
          Object.entries(composition.instruments).forEach(([name, instrument]) => {
            if (instrument.pattern[currentStep] === 1) {
              playTone(instrument.frequency, instrument.type, 0.1, instrument.volume);
            }
          });
        }, [composition.instruments, currentStep, playTone]);

        // Sequencer loop
        useEffect(() => {
          if (isPlaying) {
            const stepDuration = (60 / bpm / 4) * 1000; // 16th notes
            intervalRef.current = setInterval(() => {
              setCurrentStep((prev) => {
                const nextStep = (prev + 1) % 8;
                return nextStep;
              });
            }, stepDuration);
          } else {
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          }

          return () => {
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
            }
          };
        }, [isPlaying, bpm]);

        // Play step when currentStep changes
        useEffect(() => {
          if (isPlaying) {
            playStep();
          }
        }, [currentStep, isPlaying, playStep]);

        const startStop = () => {
          if (audioContextRef.current.state === "suspended") {
            audioContextRef.current.resume();
          }
          setIsPlaying(!isPlaying);
          if (!isPlaying) {
            setCurrentStep(0);
          }
        };

        const toggleStep = (instrumentName, stepIndex) => {
          const newPattern = [...composition.instruments[instrumentName].pattern];
          newPattern[stepIndex] = newPattern[stepIndex] === 1 ? 0 : 1;

          mergeComposition({
            instruments: {
              ...composition.instruments,
              [instrumentName]: {
                ...composition.instruments[instrumentName],
                pattern: newPattern,
              },
            },
          });
        };

        const updateInstrument = (instrumentName, field, value) => {
          mergeComposition({
            instruments: {
              ...composition.instruments,
              [instrumentName]: {
                ...composition.instruments[instrumentName],
                [field]: value,
              },
            },
          });
        };

        const saveCurrentComposition = async () => {
          if (!composition.name.trim()) {
            alert("Please enter a name for your composition");
            return;
          }

          await database.put({
            ...composition,
            type: "composition",
            createdAt: Date.now(),
          });

          // Reset to new composition
          mergeComposition({
            name: "",
            bpm: 120,
          });
        };

        const loadComposition = (comp) => {
          mergeComposition({
            name: comp.name + " (copy)",
            bpm: comp.bpm,
            instruments: comp.instruments,
          });
          setBpm(comp.bpm);
        };

        const generateRandomPattern = async () => {
          const prompt =
            "Generate a creative 8-step drum pattern for electronic music with kick, snare, hihat, bass, and lead instruments. Each pattern should be an array of 8 numbers (0 or 1) where 1 means play and 0 means rest.";

          try {
            const response = await callAI(prompt, {
              schema: {
                properties: {
                  kick: { type: "array", items: { type: "number" } },
                  snare: { type: "array", items: { type: "number" } },
                  hihat: { type: "array", items: { type: "number" } },
                  bass: { type: "array", items: { type: "number" } },
                  lead: { type: "array", items: { type: "number" } },
                },
              },
            });

            const patterns = JSON.parse(response);

            const newInstruments = { ...composition.instruments };
            Object.entries(patterns).forEach(([instrument, pattern]) => {
              if (newInstruments[instrument] && pattern.length === 8) {
                newInstruments[instrument].pattern = pattern.map((n) => Math.max(0, Math.min(1, Math.round(n))));
              }
            });

            mergeComposition({ instruments: newInstruments });
          } catch (error) {
            console.error("Failed to generate pattern:", error);
          }
        };

        return (
          <div
            className="min-h-screen p-4"
            style={{
              backgroundImage: `radial-gradient(circle at 20px 20px, #70d6ff 2px, transparent 2px),
                       radial-gradient(circle at 60px 60px, #ff70a6 2px, transparent 2px),
                       radial-gradient(circle at 100px 20px, #ffd670 2px, transparent 2px)`,
              backgroundSize: "120px 120px, 140px 140px, 100px 100px",
              backgroundColor: "#ffffff",
            }}
          >
            <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-2xl border-4 border-black">
              {/* Header */}
              <div className="bg-gradient-to-r from-ff70a6 to-70d6ff p-6 border-b-4 border-black">
                <h1 className="text-4xl font-black text-242424 mb-2">CIRCUIT GROOVE</h1>
                <p className="text-lg text-242424 italic">
                  *Create layered electronic music loops with our 8-step sequencer. Each instrument has unique oscillator tones -
                  click steps to toggle beats, adjust frequencies and volumes, then **save your masterpiece** for later playback.*
                </p>
              </div>

              {/* Controls */}
              <div className="p-6 bg-e9ff70 border-b-4 border-black">
                <div className="flex flex-wrap items-center gap-4 mb-4">
                  <button
                    onClick={startStop}
                    className="px-8 py-3 bg-ff70a6 border-4 border-black font-black text-242424 text-xl hover:bg-ff9770 transform hover:scale-105 transition-all"
                  >
                    {isPlaying ? "⏸ STOP" : "▶ PLAY"}
                  </button>

                  <div className="flex items-center gap-2">
                    <label className="font-black text-242424">BPM:</label>
                    <input
                      type="number"
                      value={bpm}
                      onChange={(e) => {
                        setBpm(parseInt(e.target.value));
                        mergeComposition({ bpm: parseInt(e.target.value) });
                      }}
                      min="60"
                      max="180"
                      className="w-20 p-2 border-4 border-black font-black text-242424"
                    />
                  </div>

                  <button
                    onClick={generateRandomPattern}
                    className="px-4 py-2 bg-70d6ff border-4 border-black font-black text-242424 hover:bg-ffd670 transform hover:scale-105 transition-all"
                  >
                    🎲 AI PATTERN
                  </button>
                </div>

                <div className="flex items-center gap-4">
                  <input
                    type="text"
                    placeholder="Composition name..."
                    value={composition.name}
                    onChange={(e) => mergeComposition({ name: e.target.value })}
                    className="flex-1 p-3 border-4 border-black font-black text-242424"
                  />
                  <button
                    onClick={saveCurrentComposition}
                    className="px-6 py-3 bg-e9ff70 border-4 border-black font-black text-242424 hover:bg-ffd670 transform hover:scale-105 transition-all"
                  >
                    💾 SAVE
                  </button>
                </div>
              </div>

              {/* Sequencer */}
              <div className="p-6">
                <div className="mb-6">
                  <h2 className="text-2xl font-black text-242424 mb-4">8-STEP SEQUENCER</h2>

                  {/* Step indicators */}
                  <div className="flex gap-2 mb-4">
                    <div className="w-32"></div>
                    {[...Array(8)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-12 h-8 border-4 border-black flex items-center justify-center font-black text-242424 ${
                          currentStep === i && isPlaying ? "bg-ff70a6" : "bg-ffffff"
                        }`}
                      >
                        {i + 1}
                      </div>
                    ))}
                  </div>

                  {/* Instruments */}
                  {Object.entries(composition.instruments).map(([name, instrument]) => (
                    <div key={name} className="mb-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div
                          className="w-32 p-2 border-4 border-black font-black text-242424 text-center"
                          style={{ backgroundColor: instrument.color }}
                        >
                          {name.toUpperCase()}
                        </div>

                        {instrument.pattern.map((active, stepIndex) => (
                          <button
                            key={stepIndex}
                            onClick={() => toggleStep(name, stepIndex)}
                            className={`w-12 h-12 border-4 border-black font-black transition-all ${
                              active ? "bg-242424 text-ffffff" : "bg-ffffff text-242424"
                            } ${currentStep === stepIndex && isPlaying ? "ring-4 ring-ff70a6" : ""}`}
                          >
                            {active ? "●" : "○"}
                          </button>
                        ))}
                      </div>

                      {/* Instrument controls */}
                      <div className="flex items-center gap-4 ml-36">
                        <label className="text-sm font-bold text-242424">Freq:</label>
                        <input
                          type="range"
                          min={name === "hihat" ? "4000" : name === "lead" ? "200" : "40"}
                          max={name === "hihat" ? "12000" : name === "lead" ? "800" : "400"}
                          value={instrument.frequency}
                          onChange={(e) => updateInstrument(name, "frequency", parseInt(e.target.value))}
                          className="w-24"
                        />
                        <span className="text-sm text-242424 w-12">{instrument.frequency}Hz</span>

                        <label className="text-sm font-bold text-242424">Vol:</label>
                        <input
                          type="range"
                          min="0.1"
                          max="1"
                          step="0.1"
                          value={instrument.volume}
                          onChange={(e) => updateInstrument(name, "volume", parseFloat(e.target.value))}
                          className="w-20"
                        />
                        <span className="text-sm text-242424 w-8">{(instrument.volume * 100).toFixed(0)}%</span>

                        <select
                          value={instrument.type}
                          onChange={(e) => updateInstrument(name, "type", e.target.value)}
                          className="px-2 py-1 border-2 border-black text-sm font-bold text-242424"
                        >
                          <option value="sine">Sine</option>
                          <option value="square">Square</option>
                          <option value="sawtooth">Sawtooth</option>
                          <option value="triangle">Triangle</option>
                        </select>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Saved Compositions */}
              {savedCompositions.length > 0 && (
                <div className="p-6 bg-ffd670 border-t-4 border-black">
                  <h3 className="text-xl font-black text-242424 mb-4">SAVED COMPOSITIONS</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {savedCompositions.map((comp) => (
                      <div key={comp._id} className="bg-ffffff border-4 border-black p-4">
                        <h4 className="font-black text-242424 mb-2">{comp.name}</h4>
                        <p className="text-sm text-242424 mb-2">{comp.bpm} BPM</p>
                        <button
                          onClick={() => loadComposition(comp)}
                          className="px-4 py-2 bg-70d6ff border-2 border-black font-bold text-242424 hover:bg-e9ff70 transition-colors"
                        >
                          LOAD
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }
      // prettier-ignore-end

      const rootElement = document.getElementById("container");
      ReactDOMClient.createRoot(rootElement).render(<App />);
    </script>
  </body>
</html>
