<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:title" content="Friends Directory App" />
    <meta property="og:description" content="Friends Directory App - remix on Vibes DIY" />

    <meta property="og:url" content="https://tokyo-mercury-1680.vibesdiy.app" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Friends Directory App" />
    <meta name="twitter:description" content="Friends Directory App - remix on Vibes DIY" />

    <meta name="twitter:url" content="https://tokyo-mercury-1680.vibesdiy.app" />

    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" sizes="any" />
    <title>Friends Directory App - made on Vibes DIY</title>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://esm.sh/use-vibes@latest/dist/components/ImgGen.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      /* Hide indicator by default, show only during network activity */
      .indicator-svg {
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      body.network-active .indicator-svg {
        opacity: 1;
      }

      /* QR code absolute positioning */
      .qr-code {
        position: fixed; /* Fixed positioning relative to viewport */
        top: 10px;
        right: 10px;
        z-index: 1001; /* Higher than position-element z-index */
        background: rgba(255, 255, 255, 0.25);
        border-radius: 14px;
        border: 1px solid rgba(35, 136, 179, 0.1);
        padding: 10px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px); /* Safari support */
        display: none; /* Hidden by default */
      }

      /* Show QR code when expanded class is added to body */
      body.expanded .qr-code {
        display: block;
      }

      /* Cyber glitch effect for Remixable text */
      @keyframes glitch-anim-1 {
        0%,
        100% {
          clip-path: inset(50% 0 30% 0);
          transform: translate(-1px, 1px);
        }
        10% {
          clip-path: inset(10% 0 60% 0);
          transform: translate(1px, -1px);
        }
        20% {
          clip-path: inset(30% 0 20% 0);
          transform: translate(1px, 0);
        }
        30% {
          clip-path: inset(20% 0 30% 0);
          transform: translate(-1px, 0);
        }
        40% {
          clip-path: inset(60% 0 10% 0);
          transform: translate(1px, 1px);
        }
        50% {
          clip-path: inset(10% 0 40% 0);
          transform: translate(-1px, -1px);
        }
        60% {
          clip-path: inset(40% 0 20% 0);
          transform: translate(1px, -1px);
        }
        70% {
          clip-path: inset(50% 0 30% 0);
          transform: translate(-1px, 1px);
        }
        80% {
          clip-path: inset(20% 0 50% 0);
          transform: translate(0, 0);
        }
        90% {
          clip-path: inset(40% 0 30% 0);
          transform: translate(-1px, 1px);
        }
      }

      @keyframes glitch-anim-2 {
        0%,
        100% {
          clip-path: inset(20% 0 40% 0);
          transform: translate(1px, 0);
        }
        10% {
          clip-path: inset(40% 0 20% 0);
          transform: translate(-1px, 1px);
        }
        20% {
          clip-path: inset(60% 0 30% 0);
          transform: translate(0, 1px);
        }
        30% {
          clip-path: inset(10% 0 60% 0);
          transform: translate(0, -1px);
        }
        40% {
          clip-path: inset(30% 0 10% 0);
          transform: translate(1px, 0);
        }
        50% {
          clip-path: inset(50% 0 40% 0);
          transform: translate(-1px, 0);
        }
        60% {
          clip-path: inset(20% 0 30% 0);
          transform: translate(1px, -1px);
        }
        70% {
          clip-path: inset(40% 0 50% 0);
          transform: translate(-1px, 1px);
        }
        80% {
          clip-path: inset(10% 0 20% 0);
          transform: translate(0, 0);
        }
        90% {
          clip-path: inset(30% 0 10% 0);
          transform: translate(1px, 1px);
        }
      }

      .remixable-text {
        position: relative;
        display: inline-block;
        margin: 0;
        color: #333;
        font-weight: bold; /* Pre-set to bold to prevent size change */
        transition: all 0.1s ease;
        z-index: 2; /* Place original text on top */
      }

      .position-element:hover .remixable-text {
        /* No animation or color change on the text itself */
        color: #333; /* Keep the original color */
      }

      @keyframes text-blur {
        0% {
          text-shadow:
            -2px 0 #ff00cc,
            2px 0 #00ccff;
        }
        25% {
          text-shadow:
            -2px 0 #00ccff,
            2px 0 #ffcc00;
        }
        50% {
          text-shadow:
            2px 0 #00ff00,
            -2px 0 #ff00cc;
        }
        75% {
          text-shadow:
            1px 0 #ffcc00,
            -1px 0 #00ccff;
        }
        100% {
          text-shadow:
            -1px 0 #ff00cc,
            1px 0 #00ff00;
        }
      }

      .position-element:hover .remixable-text:before,
      .position-element:hover .remixable-text:after {
        content: "Remixable";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.4;
        z-index: -1; /* Place behind the original text */
      }

      .position-element:hover .remixable-text:before {
        color: #ff00cc;
        animation: glitch-anim-1 8s infinite linear alternate-reverse;
      }

      .position-element:hover .remixable-text:after {
        color: #00ccff;
        animation: glitch-anim-2 60s infinite linear alternate-reverse;
      }

      /* Vibes DIY SVG logo glitch effect */
      .vibes-logo-container {
        position: relative;
        display: inline-block;
      }

      .vibes-logo-container::before,
      .vibes-logo-container::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://vibes.diy/vibes-diy.svg");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        z-index: -1;
        opacity: 0;
        transition:
          opacity 1.2s ease,
          transform 0.1s ease-out;
      }

      .vibes-logo-container {
        --moveX1: 0px;
        --moveY1: 0px;
        --moveX2: 0px;
        --moveY2: 0px;
      }

      .position-element:hover .vibes-logo-container::before {
        transform: translate(var(--moveX1), var(--moveY1));
      }

      .position-element:hover .vibes-logo-container::after {
        transform: translate(var(--moveX2), var(--moveY2));
      }

      /* Main logo transitions to color on hover */
      .vibes-logo-main {
        transition: filter 0.3s ease;
        filter: grayscale(1);
      }

      /* .position-element:hover .vibes-logo-main {
        filter: grayscale(0.2) contrast(1);
      } */

      .position-element:hover .vibes-logo-container::before,
      .position-element:hover .vibes-logo-container::after {
        opacity: 0.2;
      }

      .position-element:hover .vibes-logo-container::before {
        filter: hue-rotate(120deg) brightness(1.2) contrast(1.2) saturate(2);
        mix-blend-mode: exclusion;
      }

      .position-element:hover .vibes-logo-container::after {
        filter: hue-rotate(210deg) brightness(1.5) contrast(1.3) saturate(2.5);
        mix-blend-mode: screen;
      }

      /* Disable glitch effect when expanded */
      .position-element.expanded:hover .vibes-logo-container::before,
      .position-element.expanded:hover .vibes-logo-container::after {
        opacity: 0;
      }
      #container {
        width: 100%;
        height: 100vh;
      }

      /* Styles for expanded button state */
      .position-element.expanded .rounded-button {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        padding: 6px 10px;
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        height: 60px;
      }

      .position-element.expanded .content {
        max-width: none;
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin: 0;
        flex: 1;
      }

      .position-element.expanded .vibes-logo-container {
        margin-top: 4px;
        transform: translateY(10px);
        flex-shrink: 0;
      }

      /* Make logo larger when expanded */
      .position-element.expanded .vibes-logo-main {
        height: 52px !important;
        margin-top: -8px !important;
        filter: grayscale(0);
      }

      /* Hide/show expanded-only content */
      .expanded-only-content {
        display: none;
      }

      .position-element.expanded .expanded-only-content {
        display: flex;
        flex: 1;
        margin-left: 20px;
        width: 100%;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
      }

      /* Responsive layout for smaller screens */
      @media (max-width: 768px) {
        .position-element.expanded .expanded-only-content {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;
          margin-left: 0;
          padding: 0 12px;
        }

        .position-element.expanded .content {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;
        }

        /* Move logo to the left on mobile */
        .position-element.expanded .vibes-logo-container {
          align-self: flex-start;
          margin-left: 0;
        }

        .remix-form {
          width: 100%;
          flex-direction: column;
          gap: 8px;
        }

        /* Spread buttons apart */
        .button-group {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        }

        /* Make input wider with less padding */
        .remix-input {
          padding: 6px 12px;
          align-self: center;
          width: 100%;
          max-width: none;
        }

        .expanded-separator {
          display: none;
        }

        .position-element.expanded {
          width: 90%;
          right: 5%;
        }

        .position-element.expanded .rounded-button {
          padding: 12px 16px;
          height: auto;
          min-height: 60px;
        }
      }

      /* Even smaller screens */
      @media (max-width: 480px) {
        .position-element.expanded {
          width: 95%;
          right: 2.5%;
        }

        .remix-input {
          font-size: 16px; /* Prevents zoom on iOS */
        }
      }

      /* Remix form styles */
      .remix-form {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
      }

      .remix-input {
        flex: 1;
        min-width: 0;
        padding: 8px 14px;
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 8px;
        font-family: inherit;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.6);
        transition: all 0.2s ease;
        backdrop-filter: blur(5px);
      }

      .remix-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .remix-submit {
        background: #ccc;
        border: 1px solid rgba(59, 130, 246, 0.2);
        cursor: pointer;
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .remix-submit:hover {
        transform: scale(1.1);
      }

      .remix-link {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 22px;
        padding: 0 8px;
      }
      .remix-link:hover {
        transform: scale(1.1);
      }

      /* Add visual separators */
      .expanded-separator {
        border-left: 1px solid rgba(0, 0, 0, 0.15);
        height: 20px;
        margin: 0 8px;
      }

      /* Subtle pulse animation when collapsed */
      @keyframes gentle-pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }

      .position-element:not(.expanded) .rounded-button {
        animation: gentle-pulse 4s ease-in-out infinite;
      }

      /* Smooth fade-in for expanded content */
      .position-element.expanded .expanded-only-content {
        animation: fadeInFromRight 0.3s ease-out;
      }

      @keyframes fadeInFromRight {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .position-element {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        transition: all 0.3s ease-in-out;
      }

      .position-element.expanded {
        width: 75%;
        right: 12.5%;
      }

      .position-element:hover .rounded-button {
        background: rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        transform: translateY(-1px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;
      }
      .indicator-svg {
        position: absolute;
        top: -25px;
        left: -25px;
        z-index: 1;
        pointer-events: none; /* Allows clicking through to the SVG */
      }
      .indicator-svg img {
        width: 75px;
        height: auto;
        transition: all 0.3s ease;
        /* opacity: 0.85; */
      }

      /* Make indicator larger when expanded */
      .position-element.expanded .indicator-svg img {
        width: 100px;
      }

      .position-element.expanded .indicator-svg {
        top: -25px;
        left: -25px;
      }

      /* Adjust indicator position for smaller collapsed state */
      .indicator-svg {
        top: -19px;
        left: -19px;
      }

      /* Close button styles */
      .close-button {
        position: absolute;
        bottom: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        background: rgba(100, 100, 100, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 50%;
        display: none;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        z-index: 10;
        color: white;
      }

      .close-button:hover {
        background: rgba(80, 80, 80, 1);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
      }

      /* Show close button only when expanded */
      .position-element.expanded .close-button {
        display: flex;
      }
      .rounded-button {
        position: relative;
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.1);
        border-radius: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.6s ease;
        height: 38px;
        padding: 4px 10px;
        font-size: 14px;
        color: #333;
        text-decoration: none;
      }

      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: auto;
        margin-right: -15px;
        margin-top: 8px;
        max-width: 120px;
        transition: all 0.3s ease;
      }
    </style>
    <script>
      // Long press handler for Remix button
      document.addEventListener("DOMContentLoaded", () => {
        // Long press handler for Remix button
        const remixButton = document.querySelector(".position-element");
        if (!remixButton) return;

        let pressTimer;
        const longPressDuration = 500; // 500ms for long press

        // For touch devices
        remixButton.addEventListener("touchstart", (e) => {
          pressTimer = setTimeout(() => {
            remixButton.style.display = "none";
          }, longPressDuration);
        });

        remixButton.addEventListener("touchend", () => {
          clearTimeout(pressTimer);
        });

        remixButton.addEventListener("touchmove", () => {
          clearTimeout(pressTimer);
        });

        // For mouse devices
        remixButton.addEventListener("mousedown", (e) => {
          pressTimer = setTimeout(() => {
            remixButton.style.display = "none";
          }, longPressDuration);
        });

        remixButton.addEventListener("mouseup", () => {
          clearTimeout(pressTimer);
        });

        remixButton.addEventListener("mouseleave", () => {
          clearTimeout(pressTimer);
        });

        // Parallax effect for logo glitch based on mouse position
        const logoContainer = document.querySelector(".vibes-logo-container");
        if (logoContainer) {
          const roundedButton = document.querySelector(".rounded-button");

          roundedButton.addEventListener("mousemove", (e) => {
            // Get mouse position relative to the button
            const rect = roundedButton.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width - 0.5; // -0.5 to 0.5
            const y = (e.clientY - rect.top) / rect.height - 0.5; // -0.5 to 0.5

            // Calculate transform values (adjust multiplier for effect intensity)
            const intensity = 4;
            const moveX1 = x * intensity;
            const moveY1 = y * intensity;
            const moveX2 = x * intensity * -1.5;
            const moveY2 = y * intensity * -1.5;

            // Apply transforms using CSS custom properties
            logoContainer.style.setProperty("--moveX1", `${moveX1}px`);
            logoContainer.style.setProperty("--moveY1", `${moveY1}px`);
            logoContainer.style.setProperty("--moveX2", `${moveX2}px`);
            logoContainer.style.setProperty("--moveY2", `${moveY2}px`);
          });

          roundedButton.addEventListener("mouseleave", () => {
            // Reset positions when mouse leaves
            logoContainer.style.setProperty("--moveX1", "0px");
            logoContainer.style.setProperty("--moveY1", "0px");
            logoContainer.style.setProperty("--moveX2", "0px");
            logoContainer.style.setProperty("--moveY2", "0px");
          });
        }

        // Handle button click to resize and reposition element
        const positionElement = document.querySelector(".position-element");
        const contentLink = document.querySelector(".position-element span.content");

        if (positionElement && contentLink) {
          // Expanded state flag
          let isExpanded = false;

          // Store original styles for restoring later
          const originalStyles = {
            width: "",
            height: "",
            right: "",
            bottom: "",
            borderRadius: "",
            transform: "",
          };

          // Handle close button clicks
          const closeButton = document.querySelector(".close-button");
          if (closeButton) {
            closeButton.addEventListener("click", (e) => {
              e.stopPropagation();
              if (isExpanded) {
                // Restore original styles
                positionElement.style.width = originalStyles.width;
                positionElement.style.height = originalStyles.height;
                positionElement.style.right = originalStyles.right;
                positionElement.style.bottom = originalStyles.bottom;
                positionElement.style.borderRadius = originalStyles.borderRadius;
                positionElement.style.transform = originalStyles.transform;

                // Remove expanded class
                positionElement.classList.remove("expanded");
                // Remove expanded class from body
                document.body.classList.remove("expanded");

                isExpanded = false;
              }
            });
          }

          // Make entire position element clickable in collapsed state
          positionElement.addEventListener("click", (e) => {
            // Don't handle clicks on form elements, remix-link, or close button
            if (e.target.closest("#remix-form") || e.target.closest(".remix-link") || e.target.closest(".close-button")) {
              return;
            }

            // Handle logo clicks when expanded - navigate to vibes.diy
            if (isExpanded && e.target.closest(".vibes-logo-container")) {
              window.top.location.href = "https://vibes.diy";
              return;
            }

            // Prevent default on link clicks
            if (e.target.tagName === "A" || e.target.closest("a")) {
              e.preventDefault();
            }

            if (!isExpanded) {
              // Save original styles before changing
              originalStyles.width = positionElement.style.width;
              originalStyles.height = positionElement.style.height;
              originalStyles.right = positionElement.style.right;
              originalStyles.bottom = positionElement.style.bottom;
              originalStyles.borderRadius = positionElement.style.borderRadius;
              originalStyles.transform = positionElement.style.transform;

              // Expand and center at bottom
              positionElement.style.width = "75%";
              // Keep original height in expanded state
              positionElement.style.position = "fixed";
              positionElement.style.right = "12.5%";
              positionElement.style.bottom = "20px";
              positionElement.style.borderRadius = "16px";
              positionElement.style.transform = "none";
              positionElement.style.transition = "all 0.3s ease-in-out";
              positionElement.style.zIndex = "9999";

              // Add a class for any additional styling
              positionElement.classList.add("expanded");
              // Add expanded class to body for QR code visibility
              document.body.classList.add("expanded");

              isExpanded = true;
            } else {
              // Restore original styles
              positionElement.style.width = originalStyles.width;
              positionElement.style.height = originalStyles.height;
              positionElement.style.right = originalStyles.right;
              positionElement.style.bottom = originalStyles.bottom;
              positionElement.style.borderRadius = originalStyles.borderRadius;
              positionElement.style.transform = originalStyles.transform;

              // Remove expanded class
              positionElement.classList.remove("expanded");
              // Remove expanded class from body
              document.body.classList.remove("expanded");

              isExpanded = false;
            }
          });

          // Handle form submission
          const remixForm = document.getElementById("remix-form");
          if (remixForm) {
            remixForm.addEventListener("submit", (e) => {
              return;
              e.preventDefault();

              const input = remixForm.querySelector(".remix-input");
              const value = input.value.trim();

              if (value) {
                // Change background color based on input
                try {
                  // Set the body background color to the input value
                  document.body.style.backgroundColor = value;

                  // Show a subtle success indicator
                  input.style.backgroundColor = "rgba(220, 255, 220, 0.8)";
                  setTimeout(() => {
                    input.style.backgroundColor = "rgba(255, 255, 255, 0.5)";
                  }, 1000);

                  // Clear input
                  input.value = "";
                } catch (err) {
                  // Show error
                  input.style.backgroundColor = "rgba(255, 220, 220, 0.8)";
                  setTimeout(() => {
                    input.style.backgroundColor = "rgba(255, 255, 255, 0.5)";
                  }, 1000);
                }
              }
            });
          }
        }
      });
    </script>

    <!-- Network activity tracker script -->
    <script>
      // Network activity tracker for a single page
      (function () {
        const activeRequests = new Set();
        let lastState = null;

        // Function to update the DOM based on network state
        function updateNetworkState() {
          const currentState = activeRequests.size > 0;
          if (currentState !== lastState) {
            lastState = currentState;

            // Add or remove class on body based on network state
            if (currentState) {
              document.body.classList.add("network-active");

              // Restart SVG animations by targeting animation elements
              const indicatorImg = document.querySelector(".indicator-svg img");
              if (indicatorImg) {
                restartSvgAnimations(indicatorImg);
              }
            } else {
              document.body.classList.remove("network-active");

              // Clean up SVG animations when network activity ends
              const indicatorImg = document.querySelector(".indicator-svg img");
              if (indicatorImg) {
                cleanupSvgAnimations(indicatorImg);
              }
            }
          }
        }

        // Function to restart SVG animations
        function restartSvgAnimations(imgElement) {
          try {
            // Get the SVG document if it's an SVG
            const svgDoc = imgElement.contentDocument || imgElement.getSVGDocument?.();
            if (svgDoc) {
              // Find all animation elements and restart them
              const animations = svgDoc.querySelectorAll("animate, animateTransform, animateMotion, set");
              animations.forEach((anim) => {
                try {
                  anim.beginElement();
                } catch (e) {
                  // Fallback: manipulate begin attribute
                  const currentTime = svgDoc.documentElement.getCurrentTime?.() || 0;
                  anim.setAttribute("begin", currentTime + "s");
                }
              });
            } else {
              // For regular img elements, try to trigger re-parse by modifying src with timestamp
              const currentSrc = imgElement.src;
              if (currentSrc.includes("?")) {
                imgElement.src = currentSrc.split("?")[0] + "?t=" + Date.now();
              } else {
                imgElement.src = currentSrc + "?t=" + Date.now();
              }
            }
          } catch (e) {
            console.log("Could not restart SVG animation:", e);
          }
        }

        // Function to cleanup/stop SVG animations
        function cleanupSvgAnimations(imgElement) {
          try {
            // Get the SVG document if it's an SVG
            const svgDoc = imgElement.contentDocument || imgElement.getSVGDocument?.();
            if (svgDoc) {
              // Find all animation elements and end them
              const animations = svgDoc.querySelectorAll("animate, animateTransform, animateMotion, set");
              animations.forEach((anim) => {
                try {
                  anim.endElement();
                } catch (e) {
                  // Fallback: set begin to a future time to stop current animation
                  anim.setAttribute("begin", "indefinite");
                }
              });
            }
          } catch (e) {
            console.log("Could not cleanup SVG animation:", e);
          }
        }

        // Save original fetch function
        const originalFetch = window.fetch;

        // Override fetch to track requests
        window.fetch = (...args) => {
          const reqInfo = args[0];
          activeRequests.add(reqInfo);
          updateNetworkState();

          return originalFetch(...args)
            .then((res) => {
              if (!res.body) {
                activeRequests.delete(reqInfo);
                updateNetworkState();
                return res;
              }

              // Handle streaming responses
              const reader = res.body.getReader();
              const stream = new ReadableStream({
                start(controller) {
                  function pump() {
                    reader.read().then(({ done, value }) => {
                      if (done) {
                        activeRequests.delete(reqInfo);
                        updateNetworkState();
                        controller.close();
                        return;
                      }
                      controller.enqueue(value);
                      pump();
                    });
                  }
                  pump();
                },
              });
              return new Response(stream, { headers: res.headers });
            })
            .catch((err) => {
              // Make sure to clean up if fetch fails
              activeRequests.delete(reqInfo);
              updateNetworkState();
              throw err; // Re-throw the error
            });
        };
      })();
    </script>
  </head>
  <body>
    <div id="container"></div>

    <div class="qr-code">
      <a href="https://vibes.diy/vibe/tokyo-mercury-1680" target="_top">
        <img
          src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://vibes.diy/vibe/tokyo-mercury-1680"
          alt="QR Code"
        />
      </a>
    </div>

    <div class="position-element">
      <div class="rounded-button">
        <div class="indicator-svg">
          <img src="https://vibes.diy/indicate.svg" alt="Indicate" />
        </div>
        <div class="close-button">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          </svg>
        </div>
        <span class="content">
          <div class="vibes-logo-container">
            <img
              src="https://vibes.diy/vibes-diy.svg"
              alt="Vibes DIY"
              class="vibes-logo-main"
              style="
                height: 32px;
                display: block;
                margin: 0 auto;
                margin-top: -4px;
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
              "
            />
          </div>
          <div class="expanded-only-content flex items-center gap-2">
            <form
              id="remix-form"
              class="remix-form flex flex-grow items-center gap-2"
              action="https://vibes.diy/remix/tokyo-mercury-1680"
              target="_top"
            >
              <input type="text" name="prompt" placeholder="Make it pink" class="remix-input flex-1 min-w-0" />
              <div class="button-group flex items-center gap-2">
                <button type="submit" class="text-xs font-mono pt-1 px-1 remix-submit flex-shrink-0">
                  <span class="remixable-text">Remix</span>
                </button>
              </div>
            </form>
          </div>
        </span>
      </div>
    </div>
    <script>
      window.CALLAI_API_KEY = "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";
    </script>
    <script type="importmap">
      {
        "imports": {
          "react": "https://esm.sh/react@19.1.0/es2022/react.mjs",
          "react-dom": "https://esm.sh/react-dom@19.1.0/es2022/react-dom.mjs",
          "react-dom/client": "https://esm.sh/react-dom@19.1.0/es2022/client.mjs",
          "use-fireproof": "https://esm.sh/use-fireproof@0.22.0-dev-preview",
          "call-ai": "https://esm.sh/call-ai",
          "use-vibes": "https://esm.sh/use-vibes",
          "three": "https://esm.sh/three"
        }
      }
    </script>
    <script type="text/babel" data-type="module">
      import ReactDOMClient from "react-dom/client";

      // APP_CODE placeholder will be replaced with actual code
      // prettier-ignore
      import React, { useState } from "react"
      import { useFireproof } from "use-fireproof";
      import { callAI } from "call-ai";

      export default function App() {
        const { database, useLiveQuery, useDocument } = useFireproof("friends-directory");
        const [errors, setErrors] = useState({});

        const { doc, merge, submit, reset } = useDocument({
          name: "",
          nickname: "",
          phone: "",
          email: "",
          notes: "",
          type: "friend",
          createdAt: Date.now(),
        });

        const { docs: friends } = useLiveQuery("type", {
          key: "friend",
          descending: false,
        });

        const validateForm = () => {
          const newErrors = {};
          if (!doc.name.trim()) newErrors.name = "Name is required";
          if (doc.email && !/\S+@\S+\.\S+/.test(doc.email)) newErrors.email = "Invalid email format";
          setErrors(newErrors);
          return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = (e) => {
          e.preventDefault();
          if (validateForm()) {
            submit();
            setErrors({});
          }
        };

        const generateDemoFriends = async () => {
          const demoData = await callAI("Generate 5 diverse friend records with name, nickname, phone, email, and brief notes", {
            schema: {
              properties: {
                friends: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      nickname: { type: "string" },
                      phone: { type: "string" },
                      email: { type: "string" },
                      notes: { type: "string" },
                    },
                  },
                },
              },
            },
          });

          const friendsData = JSON.parse(demoData);
          for (const friend of friendsData.friends) {
            await database.put({
              ...friend,
              type: "friend",
              createdAt: Date.now(),
            });
          }
        };

        const deleteFriend = async (friendId) => {
          await database.del(friendId);
        };

        return (
          <div
            className="min-h-screen p-4"
            style={{
              background: `
        radial-gradient(circle at 20% 20%, #70d6ff 0%, transparent 20%),
        radial-gradient(circle at 80% 60%, #ff70a6 0%, transparent 20%),
        radial-gradient(circle at 40% 80%, #ffd670 0%, transparent 20%),
        linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)
      `,
            }}
          >
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-8 p-6 bg-white/90 rounded-3xl border-4 border-black shadow-lg">
                <h1 className="text-4xl font-black text-gray-800 mb-3">👥 Friends Directory</h1>
                <p className="text-lg font-medium text-gray-700 italic">
                  *Keep track of your friends' contact information in one colorful place. Add new friends with their details, and
                  browse through your entire network. Perfect for staying organized and never losing touch!*
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white/95 p-6 rounded-3xl border-4 border-black shadow-lg">
                  <h2 className="text-2xl font-black text-gray-800 mb-4 flex items-center">
                    <span className="w-8 h-8 bg-ff70a6 rounded-full mr-3"></span>
                    Add New Friend
                  </h2>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-1">Name *</label>
                      <input
                        type="text"
                        value={doc.name}
                        onChange={(e) => merge({ name: e.target.value })}
                        className={`w-full p-3 border-4 border-black rounded-xl font-medium ${errors.name ? "bg-red-100" : "bg-white"}`}
                        placeholder="Enter full name"
                      />
                      {errors.name && <p className="text-red-600 text-sm font-medium mt-1">{errors.name}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-1">Nickname</label>
                      <input
                        type="text"
                        value={doc.nickname}
                        onChange={(e) => merge({ nickname: e.target.value })}
                        className="w-full p-3 border-4 border-black rounded-xl font-medium bg-white"
                        placeholder="What do you call them?"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-1">Phone</label>
                      <input
                        type="tel"
                        value={doc.phone}
                        onChange={(e) => merge({ phone: e.target.value })}
                        className="w-full p-3 border-4 border-black rounded-xl font-medium bg-white"
                        placeholder="Phone number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-1">Email</label>
                      <input
                        type="email"
                        value={doc.email}
                        onChange={(e) => merge({ email: e.target.value })}
                        className={`w-full p-3 border-4 border-black rounded-xl font-medium ${errors.email ? "bg-red-100" : "bg-white"}`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && <p className="text-red-600 text-sm font-medium mt-1">{errors.email}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-800 mb-1">Notes</label>
                      <textarea
                        value={doc.notes}
                        onChange={(e) => merge({ notes: e.target.value })}
                        className="w-full p-3 border-4 border-black rounded-xl font-medium bg-white h-20"
                        placeholder="How you met, shared interests, etc."
                      />
                    </div>

                    <div className="flex gap-3">
                      <button
                        type="submit"
                        className="flex-1 bg-70d6ff hover:bg-blue-400 text-gray-800 font-black py-3 px-6 rounded-xl border-4 border-black shadow-lg transform hover:scale-105 transition-all"
                      >
                        💾 Save Friend
                      </button>
                      <button
                        type="button"
                        onClick={reset}
                        className="bg-e9ff70 hover:bg-lime-300 text-gray-800 font-black py-3 px-6 rounded-xl border-4 border-black shadow-lg"
                      >
                        🔄 Clear
                      </button>
                    </div>
                  </form>

                  {friends.length === 0 && (
                    <div className="mt-6 pt-6 border-t-4 border-black border-dashed">
                      <button
                        onClick={generateDemoFriends}
                        className="w-full bg-ff9770 hover:bg-orange-400 text-gray-800 font-black py-3 px-6 rounded-xl border-4 border-black shadow-lg transform hover:scale-105 transition-all"
                      >
                        🎲 Add Demo Friends
                      </button>
                    </div>
                  )}
                </div>

                <div className="bg-white/95 p-6 rounded-3xl border-4 border-black shadow-lg">
                  <h2 className="text-2xl font-black text-gray-800 mb-4 flex items-center justify-between">
                    <span className="flex items-center">
                      <span className="w-8 h-8 bg-ffd670 rounded-full mr-3"></span>
                      My Friends ({friends.length})
                    </span>
                  </h2>

                  {friends.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">👻</div>
                      <p className="text-gray-600 font-medium italic">No friends added yet! Start building your network above.</p>
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {friends.map((friend) => (
                        <div
                          key={friend._id}
                          className="bg-gradient-to-r from-ff70a6/20 to-70d6ff/20 p-4 rounded-2xl border-3 border-black"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h3 className="font-black text-lg text-gray-800">
                                {friend.name}
                                {friend.nickname && <span className="text-gray-600 font-medium ml-2">"{friend.nickname}"</span>}
                              </h3>

                              <div className="space-y-1 mt-2">
                                {friend.phone && (
                                  <p className="text-sm font-medium text-gray-700 flex items-center">📱 {friend.phone}</p>
                                )}
                                {friend.email && (
                                  <p className="text-sm font-medium text-gray-700 flex items-center">✉️ {friend.email}</p>
                                )}
                                {friend.notes && <p className="text-sm text-gray-600 mt-2 italic">{friend.notes}</p>}
                              </div>
                            </div>

                            <button
                              onClick={() => deleteFriend(friend._id)}
                              className="bg-red-500 hover:bg-red-600 text-white font-bold p-2 rounded-lg border-2 border-black shadow-md ml-3"
                              title="Delete friend"
                            >
                              🗑️
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6 text-center">
                <div className="bg-white/90 p-4 rounded-2xl border-4 border-black shadow-lg inline-block">
                  <p className="text-sm font-medium text-gray-600 italic">
                    **Instructions:** Fill out the form on the left to add friends to your directory. Use the *Demo Data* button to
                    get started with sample contacts. All your friends are automatically saved and synced in real-time!
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      }
      // prettier-ignore-end

      const rootElement = document.getElementById("container");
      ReactDOMClient.createRoot(rootElement).render(<App />);
    </script>
  </body>
</html>
