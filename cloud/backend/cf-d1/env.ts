/// <reference types="@cloudflare/workers-types" />
// Generated by Wrangler on Fri Aug 16 2024 13:55:06 GMT+0200 (Central European Summer Time)
// by running `wrangler types`

// import type { DurableObjectNamespace, D1Database } from "@cloudflare/workers-types";
// import { WSEvents } from "hono/ws";
import { FPRoomDurableObject } from "./server.js";
import { CFExposeCtx } from "./cf-hono-server.js";

export interface Env {
  // bucket: R2Bucket;
  // kv_store: KVNamespace;

  /** AWS/S3 access key ID for storage backend */
  ACCESS_KEY_ID: string;
  ACCOUNT_ID: string;
  BUCKET_NAME: string;
  CLOUDFLARE_API_TOKEN: string;
  EMAIL: string;
  FIREPROOF_SERVICE_PRIVATE_KEY: string;
  POSTMARK_TOKEN: string;
  SECRET_ACCESS_KEY: string;
  SERVICE_ID: string;
  STORAGE_URL: string;
  REGION: string;
  VERSION: string;
  FP_DEBUG: string;
  FP_STACK: string;
  FP_FORMAT: string;
  FP_PROTOCOL: string;
  /** Test date in ISO8601 format (YYYYMMDD'T'HHmmss'Z'). Optional. */
  TEST_DATE?: string;
  /** Maximum idle time in seconds before connection timeout. Optional. */
  MAX_IDLE_TIME?: string;

  // default D1
  CF_BACKEND_MODE: "D1" | "DURABLE_OBJECT";
  // default D1 "FP_BACKEND_D1"
  // default DURABLE_OBJECT "FP_BACKEND_DO"
  CF_BACKEND_KEY?: string;

  FP_BACKEND_D1: D1Database;

  // FP_BACKEND_DO: DurableObjectNamespace<FPBackendDurableObject>;
  // default CF_BACKEND_KEY
  FP_BACKEND_DO_ID: string;

  // default "FP_WS_ROOM"
  CF_WS_ROOM_KEY: string;

  FP_WS_ROOM: DurableObjectNamespace<FPRoomDurableObject>;

  FP_EXPOSE_CTX: CFExposeCtx;

  // WS_EVENTS: WSEvents;
}

// declare module "cloudflare:test" {
//   // ...or if you have an existing `Env` type...
//   interface ProvidedEnv extends Env {
//     readonly test: boolean;
//   }
// }
