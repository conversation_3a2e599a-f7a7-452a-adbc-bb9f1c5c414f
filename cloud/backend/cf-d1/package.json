{"name": "@fireproof/cloud-backend-cf-d1", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "scripts": {"build": "core-cli tsc", "test": "vitest --run", "pack": "echo cloud need not to pack", "publish": "echo skip", "drizzle:d1-local": "drizzle-kit push --config ./drizzle.cloud.d1-local.config.ts", "drizzle:d1-remote": "drizzle-kit push --config ./drizzle.cloud.d1-remote.config.ts", "wrangler:deploy": "wrangler deploy -c ./wrangler.toml --env dev"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "dependencies": {"@adviser/cement": "^0.4.21", "@cloudflare/workers-types": "^4.20250719.0", "@fireproof/cloud-backend-base": "workspace:0.0.0", "@fireproof/cloud-base": "workspace:0.0.0", "@fireproof/core-protocols-cloud": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/core-types-protocols-cloud": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "cmd-ts": "^0.13.0", "drizzle-orm": "^0.44.3", "hono": "^4.8.9", "multiformats": "^13.3.7"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "drizzle-kit": "0.30.6", "vitest": "^3.2.4", "wrangler": "^4.26.0", "zx": "^8.7.1"}}