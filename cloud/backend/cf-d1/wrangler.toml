name = "fireproof-v2-cloud"
main = "server.ts"
compatibility_date = "2025-02-24"
# not really needed, but there are some warning of type imports
compatibility_flags = ["nodejs_compat"]
# upload_source_maps = true

# [durable_objects]
# bindings = [
#   { name = "FP_DO", class_name = "FPDurableObject"},
# ]

# [[d1_databases]]
# binding = "DB"
# database_name = "test-meta-merge"
# database_id = "b8b452ed-b1d9-478f-b56c-c5e4545f157a"

[durable_objects]
bindings = [
  # { name = "FP_BACKEND_DO", class_name = "FPBackendDurableObject" },
  { name = "FP_WS_ROOM", class_name = "FPRoomDurableObject" }
]

[[migrations]]
tag = "v1"
new_classes = [ "FPRoomDurableObject" ]

# [[migrations]]
# tag = "v1" # Should be unique for each entry
# new_sqlite_classes = ["FPBackendDurableObject"]

[observability]
enabled = true
head_sampling_rate = 1

[env.test.vars]
VERSION = "FP-MSG-1.0"
#STORAGE_URL = "http://127.0.0.1:9000/testbucket"
#ACCESS_KEY_ID = "minioadmin"
#SECRET_ACCESS_KEY = "minioadmin"
#FP_DEBUG = "FPMetaGroups"

# [[env.test.migrations]]
# tag = "v1" # Should be unique for each entry
# new_sqlite_classes = ["FPBackendDurableObject"]

[env.test.durable_objects]
bindings = [
  # { name = "FP_BACKEND_DO", class_name = "FPBackendDurableObject" },
  { name = "FP_WS_ROOM", class_name = "FPRoomDurableObject" }
]

[[env.test.d1_databases]]
binding = "FP_BACKEND_D1"
database_name = "test-meta-merge"
database_id = "b0c1ea22-b733-420c-b812-bea9ffaa1676"

[env.dev.vars]
VERSION = "FP-MSG-1.0"
#CLOUD_SESSION_TOKEN_PUBLIC = "should-be-set-by-dot-dev"
#STORAGE_URL = "should-be-set-by-dot-dev"


# STORAGE_URL = "https://f031392067b661e91963881fb76b4ea3.r2.cloudflarestorage.com"
#FP_DEBUG = "FPMetaGroups"

[[env.dev.migrations]]
tag = "v1" # Should be unique for each entry
#new_sqlite_classes = ["FPBackendDurableObject"]
new_classes = ["FPRoomDurableObject"]

[env.dev.durable_objects]
bindings = [
  # { name = "FP_BACKEND_DO", class_name = "FPBackendDurableObject" },
  { name = "FP_WS_ROOM", class_name = "FPRoomDurableObject" }
]

[[env.dev.d1_databases]]
binding = "FP_BACKEND_D1"
database_name = "fp-cloud-dev"
database_id = "b0c1ea22-b733-420c-b812-bea9ffaa1676"

