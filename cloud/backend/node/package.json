{"name": "@fireproof/cloud-backend-node", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "scripts": {"build": "core-cli tsc", "test": "vitest --run", "pack": "echo cloud need no pack", "publish": "echo skip"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/cloud-backend-base": "workspace:0.0.0", "@fireproof/cloud-base": "workspace:0.0.0", "@fireproof/core-base": "workspace:0.0.0", "@fireproof/core-protocols-cloud": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/core-types-protocols-cloud": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "@hono/node-server": "^1.17.1", "@hono/node-ws": "^1.2.0", "@libsql/client": "^0.15.10", "drizzle-orm": "^0.44.3", "hono": "^4.8.9", "vitest": "^3.2.4"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@types/node": "^24.1.0", "drizzle-kit": "0.30.6", "zx": "^8.7.1"}}