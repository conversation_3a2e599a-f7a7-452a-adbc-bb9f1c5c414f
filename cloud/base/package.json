{"name": "@fireproof/cloud-base", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "scripts": {"build": "core-cli tsc", "pack": "echo cloud need not to pack", "publish": "echo skip"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "peerDependencies": {"react": ">=18.0.0"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/core-blockstore": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/vendor": "workspace:0.0.0", "aws4fetch": "^1.0.20", "cmd-ts": "^0.13.0", "jose": "^6.0.12"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "vitest": "^3.2.4", "zx": "^8.7.1"}}