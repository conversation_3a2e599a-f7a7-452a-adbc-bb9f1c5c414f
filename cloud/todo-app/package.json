{"name": "@fireproof/cloud-todo-app", "version": "0.0.0", "description": "Live ledger for the web.", "type": "module", "module": "./index.js", "main": "./index.cjs", "types": "./index.d.ts", "scripts": {"build": "core-cli tsc", "pack": "echo cloud need not to pack", "publish": "echo skip"}, "keywords": ["ledger", "JSON", "document", "IPLD", "CID", "IPFS"], "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "AFL-2.0", "homepage": "https://use-fireproof.com", "gptdoc": "import { fireproof } from 'use-fireproof'; const db = fireproof('app-db-name'); const ok = await db.put({ anyField: ['any','json'] }); const doc = await db.get(ok.id); await db.del(doc._id); db.subscribe(myRedrawFn); const result = await db.query('anyField', {range : ['a', 'z']}); result.rows.map(({ key }) => key);", "repository": {"type": "git", "url": "git+https://github.com/fireproof-storage/fireproof.git"}, "bugs": {"url": "https://github.com/fireproof-storage/fireproof/issues"}, "peerDependencies": {"react": ">=18.0.0"}, "dependencies": {"@adviser/cement": "^0.4.21", "@fireproof/vendor": "workspace:0.0.0", "@types/react": "^19.1.8", "react-dom": "^19.1.0", "use-fireproof": "workspace:0.0.0"}, "devDependencies": {"@fireproof/core-cli": "workspace:0.0.0", "@types/react-dom": "^19.1.6"}}