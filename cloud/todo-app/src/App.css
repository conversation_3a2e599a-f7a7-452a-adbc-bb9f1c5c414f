.app {
  max-width: 600px;
  width: 100%;
  padding: 1.5rem;
  text-align: left;
  box-sizing: border-box;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
}

.app-header h1 {
  color: #646cff;
  margin-bottom: 0.5rem;
}

.sync-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: rgba(100, 108, 255, 0.1);
  border-radius: 8px;
  font-size: 0.9em;
}

.sync-status button {
  padding: 0.4em 0.8em;
  font-size: 0.8em;
}

.todo-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.todo-form input {
  flex: 1;
}

.todo-form button {
  background-color: #646cff;
  color: white;
  border: none;
}

.todo-form button:hover {
  background-color: #535bf2;
}

.todo-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.filters button {
  padding: 0.4em 0.8em;
  font-size: 0.9em;
}

.filters button.active {
  background-color: #646cff;
  color: white;
  border-color: #646cff;
}

.todo-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
}

.todo-checkbox {
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
}

.todo-text {
  flex: 1;
  font-size: 1.1em;
}

.todo-actions {
  display: flex;
  gap: 0.5rem;
}

.todo-actions button {
  padding: 0.3em 0.6em;
  font-size: 0.8em;
}

.delete-btn {
  background-color: #ff4757;
  color: white;
  border: none;
}

.delete-btn:hover {
  background-color: #ff3742;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  opacity: 0.6;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .todo-form {
    flex-direction: column;
    gap: 1rem;
  }

  .todo-form input {
    width: 100%;
  }

  .todo-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .todo-text {
    font-size: 1rem;
  }

  .filters {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .filters button {
    flex: 1;
    min-width: 80px;
  }

  .sync-status {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .app {
    padding: 0.75rem;
  }

  .app-header h1 {
    font-size: 1.75rem;
  }

  .todo-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .todo-actions button {
    padding: 0.25em 0.5em;
    font-size: 0.75em;
  }
}

@media (prefers-color-scheme: light) {
  .todo-item {
    background-color: rgba(0, 0, 0, 0.02);
    border-color: rgba(0, 0, 0, 0.1);
  }

  .sync-status {
    background-color: rgba(100, 108, 255, 0.05);
  }
}
