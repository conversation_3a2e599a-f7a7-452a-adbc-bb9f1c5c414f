{"name": "@fireproof/dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite dev", "backend:deno": "deno run --unstable-sloppy-imports --allow-net --allow-read --allow-ffi  --allow-env --env-file=.env.local backend/deno-serve.js", "backend:d1": "wrangler dev -c wrangler.toml --port 7370", "deploy:cf": "wrangler deploy -c wrangler.toml", "build": "core-cli tsc; pnpm run build:vite", "build:vite": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest --run", "format": "prettier --config ../.prettierrc .", "check": "pnpm format --write && core-cli tsc --noEmit && pnpm lint && pnpm test && pnpm build", "drizzle:libsql": "drizzle-kit push --config ./drizzle.libsql.config.ts", "drizzle:d1-local": "drizzle-kit push --config ./drizzle.d1-local-backend.config.ts", "drizzle:d1-remote": "drizzle-kit push --config ./drizzle.d1-remote.config.ts", "pack": "echo dashboard need not to pack", "publish": "echo skip"}, "dependencies": {"@adviser/cement": "^0.4.21", "@clerk/backend": "^2.6.0", "@clerk/clerk-js": "^5.77.0", "@clerk/clerk-react": "^5.37.0", "@fireproof/core": "workspace:0.0.0", "@fireproof/core-protocols-cloud": "workspace:0.0.0", "@fireproof/core-protocols-dashboard": "workspace:0.0.0", "@fireproof/core-runtime": "workspace:0.0.0", "@fireproof/core-types-base": "workspace:0.0.0", "@fireproof/core-types-protocols-cloud": "workspace:0.0.0", "@fireproof/vendor": "^3.0.0", "@monaco-editor/react": "^4.7.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.83.0", "highlight.js": "^11.10.0", "i": "^0.3.7", "jose": "^6.0.12", "minimatch": "^10.0.1", "multiformats": "^13.3.6", "partysocket": "^1.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-router-dom": "^7.7.1", "react-simple-code-editor": "^0.14.1", "use-editable": "^2.3.3", "use-fireproof": "workspace:0.0.0"}, "devDependencies": {"@clerk/clerk-react": "^5.37.0", "@cloudflare/vite-plugin": "^1.10.1", "@cloudflare/workers-types": "^4.20250710.0", "@eslint/js": "^9.32.0", "@fireproof/core-cli": "workspace:0.0.0", "@libsql/client": "^0.15.10", "@libsql/kysely-libsql": "^0.4.1", "@rollup/plugin-replace": "^6.0.1", "@testing-library/react": "^16.3.0", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.20", "drizzle-kit": "0.30.6", "drizzle-orm": "^0.44.3", "eslint": "^9.32.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^16.2.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4", "wrangler": "^4.26.0", "zx": "^8.7.1"}}