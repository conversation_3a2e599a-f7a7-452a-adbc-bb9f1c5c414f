name: "dashboard-base"
description: "Dashboard-Base Compile & Test Action"
inputs:
  CLOUDFLARE_API_TOKEN:
    description: "Cloudflare API token for authentication"
    required: true
  CLOUDFLARE_ACCOUNT_ID:
    description: "Cloudflare account ID"
    required: true
  CLOUDFLARE_D1_TOKEN:
    description: "Cloudflare D1 token for database access"
    required: true
  CLOUDFLARE_DATABASE_ID:
    description: "Cloudflare database ID"
    required: true
  CLOUD_SESSION_TOKEN_PUBLIC:
    description: "Public session token for cloud authentication"
    required: true
  CLOUD_SESSION_TOKEN_SECRET:
    description: "Secret session token for cloud authentication"
    required: true
  CLERK_PUBLISHABLE_KEY:
    description: "Clerk publishable key"
    required: true
  CLERK_PUB_JWT_URL:
    description: "Clerk public JWT URL"
    required: true
  CLERK_PUB_JWT_KEY:
    description: "Clerk public JWT key"
    required: true
  CLOUDFLARE_ENV:
    description: "Cloudflare environment (e.g., production, staging)"
    required: true

runs:
  using: "composite"
  steps:
    - name: Deploy Dashboard
      working-directory: dashboard
      shell: bash
      env:
        # need for drizzle
        CLOUDFLARE_API_TOKEN: ${{ inputs.CLOUDFLARE_API_TOKEN }}
        CLOUDFLARE_ACCOUNT_ID: ${{ inputs.CLOUDFLARE_ACCOUNT_ID }}
        CLOUDFLARE_D1_TOKEN: ${{ inputs.CLOUDFLARE_D1_TOKEN }}
        CLOUDFLARE_DATABASE_ID: ${{ inputs.CLOUDFLARE_DATABASE_ID }}
        # need in CF to be Env
        CLOUDFLARE_ENV: ${{ inputs.CLOUDFLARE_ENV }}
        CLOUD_SESSION_TOKEN_PUBLIC: ${{ inputs.CLOUD_SESSION_TOKEN_PUBLIC }}
        CLOUD_SESSION_TOKEN_SECRET: ${{ inputs.CLOUD_SESSION_TOKEN_SECRET }}
        CLERK_PUBLISHABLE_KEY: ${{ inputs.CLERK_PUBLISHABLE_KEY }}
        CLERK_PUB_JWT_KEY: ${{ inputs.CLERK_PUB_JWT_KEY }}
        CLERK_PUB_JWT_URL: ${{ inputs.CLERK_PUB_JWT_URL }}
        # need during build
        VITE_CLERK_PUBLISHABLE_KEY: ${{ inputs.CLERK_PUBLISHABLE_KEY }}
      run: |
        pnpm run drizzle:d1-remote
        pnpm exec core-cli writeEnv --env ${{inputs.CLOUDFLARE_ENV}} --out /dev/stdout --json \
          --fromEnv CLERK_PUBLISHABLE_KEY \
          --fromEnv CLERK_PUB_JWT_URL \
          --fromEnv CLERK_PUB_JWT_KEY \
          --fromEnv CLOUD_SESSION_TOKEN_SECRET \
          --fromEnv CLOUD_SESSION_TOKEN_PUBLIC | \
          pnpm exec wrangler -c ./wrangler.toml secret --env ${{inputs.CLOUDFLARE_ENV}} bulk
        pnpm run deploy:cf --env ${{inputs.CLOUDFLARE_ENV}}

      # - name: deploy to production
      #   if: github.ref == 'refs/heads/main'
      #   env:
      #     CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      #     CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
      #     CLOUDFLARE_DATABASE_ID: ${{ secrets.CLOUDFLARE_DATABASE_ID }}
      #   run: |
      #     pnpm run build
      #     pnpm run drizzle:d1-remote
      #     pnpm run deploy:cf --env production
