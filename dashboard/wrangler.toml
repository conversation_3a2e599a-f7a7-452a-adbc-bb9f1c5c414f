name = "fireproof-dashboard"
main = "backend/cf-serve.ts"
compatibility_date = "2025-02-24"
compatibility_flags = ["nodejs_compat"]

vars = { ENVIRONMENT = "test" }

#[observability.logs]
#enabled = true

[assets]
directory = "dist/static/client"
binding = "ASSETS"
html_handling = "force-trailing-slash"
#not_found_handling = "single-page-application"
#run_worker_first = true


[[d1_databases]]
binding = "DB"
database_name = "test-auth-db"
database_id = "ccb4a6c9-a1da-4a3a-9895-86d6eb3ffd26"

#[[d1_databases]]
#binding = "DB"
#database_name = "test-2-db"
#database_id = "e62e4150-0405-4f17-b201-1f242f8c3c94"

[env.dev]
vars = { ENVIRONMENT = "dev" }
routes = [
  { pattern = "dev.connect.fireproof.direct", custom_domain = true }
]

[[env.dev.d1_databases]]
binding = "DB"
database_name = "fp-connect-dev"
database_id = "04b54bac-85e4-4a4d-9527-bce490761b12"


[env.staging]
vars = { ENVIRONMENT = "staging" }
routes = [{pattern = "staging.connect.fireproof.direct", custom_domain = true}]


[[env.staging.d1_databases]]
binding = "DB"
database_name = "fp-connect-staging"
database_id = "88b9e2e8-8ad6-4820-9ff8-e11c8208a5e5"

[env.production]
vars = { ENVIRONMENT = "production" }
routes = [{pattern = "connect.fireproof.direct", custom_domain = true}]


[[env.production.d1_databases]]
binding = "DB"
database_name = "fp-connect-production"
database_id = "cb0638e7-27e4-445a-9070-9fa081f331e6"


