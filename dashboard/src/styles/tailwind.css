@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Geist Mono";
  src: url("/fonts/GeistMono-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Geist";
  src: url("/fonts/GeistVF.woff2") format("woff2-variations");
  font-weight: 600 700;
  font-style: normal;
}

@font-face {
  font-family: "Inter Tight";
  src: url("/fonts/InterTight-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Inter Tight";
  src: url("/fonts/InterTight-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

/* VARIABLES */

:root {
  --fp-color-primary: rgba(44, 44, 44, 1);
  --fp-color-secondary: rgba(44, 44, 44, 1);
  --fp-color-accent-00: rgba(249, 161, 0, 1);
  --fp-color-accent-01: rgba(245, 135, 9, 1);
  --fp-color-accent-02: rgba(241, 108, 18, 1);
  --fp-color-accent-03: rgba(238, 82, 28, 1);
  --fp-color-decorative-00: rgba(236, 234, 234, 1);
  --fp-color-decorative-01: rgba(224, 222, 222, 1);
  --fp-color-decorative-02: rgba(44, 44, 44, 0.4);
  --fp-color-background-00: rgba(255, 255, 255, 1);
  --fp-color-background-01: rgba(245, 245, 245, 1);
  --fp-color-background-02: rgba(240, 240, 240, 1);
  --fp-color-red: rgba(200, 32, 32, 1);
  --fp-color-green: rgba(47, 160, 94, 1);

  --font-family-main: "Geist";
  --font-family-body: "Inter Tight";
  --font-family-mono: "Geist Mono";
}

.dark {
  --fp-color-primary: rgba(255, 255, 255, 1);
  --fp-color-secondary: rgba(255, 255, 255, 0.6);
  --fp-color-accent-00: rgba(255, 170, 15, 1);
  --fp-color-accent-01: rgba(255, 143, 15, 1);
  --fp-color-accent-02: rgba(255, 113, 25, 1);
  --fp-color-accent-03: rgba(255, 87, 29, 1);
  --fp-color-decorative-00: rgba(48, 47, 48, 1);
  --fp-color-decorative-01: rgba(65, 65, 65, 1);
  --fp-color-decorative-02: rgba(255, 255, 255, 0.3);
  --fp-color-background-00: rgba(23, 22, 22, 1);
  --fp-color-background-01: rgb(32, 31, 32, 1);
  --fp-color-background-02: rgb(32, 31, 32, 1);
  --fp-color-red: rgba(247, 75, 75, 1);
  --fp-color-green: rgba(40, 208, 111, 1);
}

/* TEXT STYLES */

@layer utilities {
  .text-34 {
    @apply text-xxl leading-xxl font-main font-bold tracking-tighter;
  }

  .text-20 {
    @apply text-xl leading-xl font-main font-semibold tracking-tight;
  }

  .text-16 {
    @apply text-l leading-l font-body font-medium tracking-wide;
  }

  .text-14-heading {
    @apply text-m leading-l font-main font-semibold tracking-tight;
  }

  .text-14 {
    @apply text-m leading-m font-body font-medium tracking-wide;
  }

  .text-14-bold {
    @apply text-m leading-m font-body font-bold tracking-wide;
  }

  .text-11 {
    @apply text-xs leading-xs font-main font-semibold tracking-wide uppercase;
  }

  .text-code {
    @apply text-s leading-s font-mono font-medium tracking-wide;
  }
}

/* GLOBAL */

:root {
  background-color: var(--fp-color-background-00);
}

/* temporary hack to fix dark highlight.js theme */
.dark .language-json code,
.dark .language-javascript code {
  filter: brightness(2.5) contrast(0.6);
}

body {
  font-family: var(--font-family-body), "Inter", "sans-serif;";
  font-weight: 500;
  letter-spacing: 0.02em;
  font-size: 14px;
  color: var(--fp-color-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

code,
pre {
  font-family: "Geist Mono";
}

::-webkit-scrollbar {
  width: 0px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--fp-color-decorative-01);
  border-radius: 3px;
  cursor: grab;
}
